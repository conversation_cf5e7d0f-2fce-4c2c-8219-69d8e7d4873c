/* Detail Container */
.detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 40rpx;
}

/* Navigation Bar */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.nav-back {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-back:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

.back-icon {
  font-size: 32rpx;
  font-weight: 600;
}

.back-text {
  font-size: 28rpx;
}

/* Header Section */
.detail-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 148rpx 40rpx 80rpx; /* Adjusted top padding for nav bar */
  color: white;
  overflow: hidden;
}

.detail-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 2;
}

.article-title {
  font-size: 44rpx;
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: 32rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.article-meta {
  display: flex;
  gap: 32rpx;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  opacity: 0.9;
}

.meta-icon {
  font-size: 28rpx;
}

.meta-text {
  font-weight: 400;
}

.header-decoration {
  position: absolute;
  bottom: -2rpx;
  left: 0;
  right: 0;
  height: 40rpx;
  background: #f5f7fa;
  border-radius: 40rpx 40rpx 0 0;
}

/* Content Section */
.content-section {
  padding: 0 30rpx;
  margin-top: -20rpx;
  position: relative;
  z-index: 3;
}

.content-wrapper {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-body {
  padding: 40rpx;
  line-height: 1.8;
  font-size: 30rpx;
  color: #2c3e50;
}

/* wxParse Content Styling */
.content-body .wxParse-p {
  margin-bottom: 32rpx;
  line-height: 1.8;
  text-align: justify;
}

.content-body .wxParse-h1,
.content-body .wxParse-h2,
.content-body .wxParse-h3 {
  font-weight: 600;
  color: #2c3e50;
  margin: 48rpx 0 24rpx;
  line-height: 1.4;
}

.content-body .wxParse-h1 {
  font-size: 40rpx;
  border-left: 8rpx solid #667eea;
  padding-left: 24rpx;
}

.content-body .wxParse-h2 {
  font-size: 36rpx;
  border-left: 6rpx solid #764ba2;
  padding-left: 20rpx;
}

.content-body .wxParse-h3 {
  font-size: 32rpx;
  border-left: 4rpx solid #95a5a6;
  padding-left: 16rpx;
}

.content-body .wxParse-img {
  width: 100% !important;
  height: auto !important;
  border-radius: 16rpx;
  margin: 32rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.content-body .wxParse-strong {
  font-weight: 600;
  color: #667eea;
}

.content-body .wxParse-em {
  font-style: italic;
  color: #7f8c8d;
}

.content-body .wxParse-ul,
.content-body .wxParse-ol {
  margin: 24rpx 0;
  padding-left: 40rpx;
}

.content-body .wxParse-li {
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.content-body .wxParse-blockquote {
  background: #f8f9fa;
  border-left: 6rpx solid #667eea;
  padding: 24rpx;
  margin: 32rpx 0;
  border-radius: 0 16rpx 16rpx 0;
  font-style: italic;
  color: #5a6c7d;
}

/* Footer Section */
.detail-footer {
  margin-top: 60rpx;
  padding: 40rpx;
  text-align: center;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.footer-text {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
}

.footer-decoration {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.decoration-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  animation: pulse 2s infinite;
}

.decoration-dot:nth-child(2) {
  animation-delay: 0.5s;
}

.decoration-dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Legacy styles removed - replaced with modern design above */