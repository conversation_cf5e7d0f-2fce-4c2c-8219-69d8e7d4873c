<!-- 水票 -->
<view class='page'>
  <view class='cartMain'>
    <view class='tips'>Tips: 请勾选您想要使用的水票。</view>
    <view wx:for="{{cartItems}}">
      <view data-id="{{item.id}}" class='cart-box df bg-white' style='transition: all 0.5s; {{list[index].Style}}' data-index="{{index}}">
        <view class='icon '>
          <view wx:if="{{item.isUsable == 1}}">
            <view wx:if="{{is_select[index].selected}}">
              <image data-index="{{index}}" bindtap="selectedCart" src='/images/check-yes-blue.png'></image>
            </view>
            <view wx:else>
              <image bindtap="selectedCart" data-index="{{index}}" src='/images/check-no.png'></image>
            </view>
          </view>
          <view wx:else>
            <image data-index="{{index}}" src='/images/disabled.png'></image>
          </view>
        </view>
        <view class="bg-white">
          <view class="flex " style="padding:24rpx 24rpx 0 24rpx;justify-content: space-between;align-items: center;">
            <view class="font-size-24" style="color:#4ece1c">本次交易该水票可直接兑换商品</view>
            <view class="flex font-size-26" style="align-items: flex-end">
              <text>剩余可用</text>
              <image wx:for="{{item.split}}" class='img' style="width:32rpx;height:46rpx;margin:0 10rpx;"  src='/images/number/{{item}}.png'></image>
              <text>张</text>
            </view>
          </view>
          <view wx:if="{{item.isUsable == 1}}" class='cart_main df' style='background:url("{{item.background}}") ;background-size:100% 100%;'>
            <view class='left' wx:if="{{item.disprice}}" style="margin-top:-16rpx;">
              <view class='font-size-24 color-red'>
                <text>￥</text>
                <text class='font-size-42 bold color-red'>{{item.disprice}}</text>
                <view>元/张</view>
              </view>
              <view class='font-size-20' style="text-decoration: line-through">
                <text>￥</text>
                <text class=''>{{item.price}}</text> 元/张
              </view>
            </view>
            <view class='left' wx:else>
              <view class='font-size-24 margin-bottom-10'>
                <text>￥</text>
                <text class='font-size-42 bold '>{{item.price}}</text>
                <view>元/张</view>
              </view>
            </view>
            
            <!-- <view class='left' style="width:180rpx">
              <view class='dot'></view>
              <view class='i1 font-size-26 '>当前剩余可用</view>
              <view class='i2'>
                <image wx:for="{{item.split}}" class='img' src='/images/number/{{item}}.png'></image>
                <text class='num'>张</text>
              </view>
              <view class='i3'>尽享折扣</view>
            </view> -->
            <view class='right'>
              <!-- <view>品牌：{{item.brand}}</view> -->
              <view class='rule'>适用于：{{item.specification}}</view>
              <!-- <view>订水电话：{{item.phone}}</view> -->
            </view>
          </view>
          <view wx:else class='cart_main df' style='background:url("{{imgUri}}/images/gray.png") ;background-size:100% 100%;'>
            <view class='left' wx:if="{{item.disprice}}" style="margin-top:-16rpx;">
              <view class='font-size-24 color-red'>
                <text>￥</text>
                <text class='font-size-42 bold color-red'>{{item.disprice}}</text>
                <view>元/张</view>
              </view>
              <view class='font-size-20' style="text-decoration: line-through">
                <text>￥</text>
                <text class=''>{{item.price}}</text> 元/张
              </view>
            </view>
            <view class='left' wx:else>
              <view class='font-size-24 margin-bottom-10'>
                <text>￥</text>
                <text class='font-size-42 bold '>{{item.price}}</text>
                <view>元/张</view>
              </view>
            </view>
            <view class='right'>
              <!-- <view>品牌：{{item.brand}}</view> -->
              <view class='rule'>适用于：{{item.specification}}</view>
              <!-- <view>订水电话：{{item.phone}}</view> -->
            </view>
          </view>
        </view>

      </view>
      <view wx:if="{{item.isUsable == 0}}" class='no-use'>不可用原因：产品规格不符</view>
    </view>
  </view>
</view>
<view class='botton-box' bindtap='sureUseTicket'>确定使用</view>