/* common/nyz_area_picker/nyz_area_picker.wxss */
.nyz_area_view{
  width: 100%;
  position: fixed;
  bottom:0px;
  left: 0px;
  background-color: #fff;
  z-index: 21;
}

.nyz_area_pick_view{
  height: 180px;
  width: 100%;
}

.nyz_area_colum_view{
  line-height: 35px;
  text-align: center;
  font-size: 28rpx;
}

.hide{
  display: none;
}

.show{
  display: block;
}

.nyz_area_view_btns{
  background-color: #fff;
  border-bottom: 1px solid #eeeeee;
  font-size: 30rpx;
  padding: 18rpx 0rpx;
}

.nyz_area_view_btns>text{
  display: inline-block;
  word-spacing: 4rpx;
  letter-spacing: 4rpx;
}

.nyz_area_view_btn_cancle{
  color: #939393;
  padding-right: 20rpx;
  padding-left: 25rpx;
}

.nyz_area_view_btn_sure{
  float: right;
  padding-left: 20rpx;
  padding-right: 25rpx;
}

.nyz_area_mask{
  width: 100%;
  height: 100vh;
  background-color: rgba(8, 8, 8, 0.8);
  position: absolute;
  top: 0rpx;
  left: 0rpx;
  z-index: 20;
}