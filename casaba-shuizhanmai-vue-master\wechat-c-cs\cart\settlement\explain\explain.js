// cart/index/index.js
import config from '../../../config.js';
var app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    explain:[],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var storeId = wx.getStorageSync('shopInfo').szmCStore.storeId;
    var _this = this;
    app.request({
      url: config.goExplain,
      data:{
        shippingfee: storeId,
      },
      success(res){
        console.log(res);
        if(res.data.code == 1){
          _this.setData({
            explain:res.data.data
          })
        }else{
          
        }
      }
    })
  },
  // 请选择个人信息
  chooseInfo:function(e){

  },  
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },


})
