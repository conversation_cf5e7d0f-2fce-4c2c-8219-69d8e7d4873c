// component.js
import config from '../../config.js';
import util from '../../utils/util.js';
var app = getApp();
Component({
  lifetimes: {
    attached: function() {
      // 在组件实例进入页面节点树时执行
      this.addressLoad();
      if (this.data.invoice) {
        this.setData({
          invoiceInfo: this.data.invoice
        })
      };
      if (this.data.acceptTypeOnly) {
        let acceptType = "invoiceInfo.acceptType"
        this.setData({
          [acceptType]: 1
        })
      }
      this.setData({
        canChooseInvoiceSpecial: util.judgeModel('zp'),
      })
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  // 组件属性
  properties: {
    //输入框密码位数
    value_length: {
      type: Number,
      value: 0,
      // 监听输入框密码变化
      observer: function(newVal, oldVal) {}
    },
    //输入框高度
    height: {
      type: String,
      value: "700rpx",
      observer: function(newVal, oldVal) {

      }
    },
    //输入框宽度
    isModal: {
      type: Boolean,
      value: false,
      observer: function(newVal, oldVal) {

      }
    },
    acceptTypeOnly: {
      type: Boolean,
      value: false,
      observer: function(newVal, oldVal) {

      }
    },
    store: {
      type: Object,
      observer: function(newVal, oldVal) {

      }
    },
    // 初始对象
    invoice: {
      type: Object,
      value: false,
      observer: function(newVal, oldVal) {}
    }
  },

  // 初始化数据
  data: {
    imgUri: app.imgUri,
    invoiceInfo: {
      invoiceType: 0, //发票类型 0普通发票 1专用发票
      riseType: 0, //抬头类型 0个人 1单位
      unitName: '', //单位名称
      taxNum: '', //税号
      unitAddress: '', //单位地址
      unitPhone: '', //单位电话
      bankName: '', //开户行
      bankNum: '', //银行账号
      moreContent: '', //更多内容
      acceptType: 0, //接受方式 0随发货接收 1快递接收
      acceptAddress: '', //邮寄地址
      acceptAddressMore: '', //详细地址
      acceptName: '', //收件人姓名
      acceptPhone: '', //收件人手机号
      email: '', //收件人手机号
      mobile: '', //收件人手机号
    },
    canChooseInvoiceSpecial: false,
    areaRange: [],
    // 开票对象历史信息
    invoiceHistoryList: [],
    showInvoiceHistory: false
  },

  // 组件方法
  methods: {
    copyText: function() {
      // 使用微信的API来复制文本
      wx.setClipboardData({
        data: this.data.store.storePhone,
        success: function() {
          wx.showToast({
            title: '复制成功',
            icon: 'success',
            duration: 2000
          });
        }
      });
    },
    changetttt(e) {
      this.setData({
        'invoiceInfo.riseType': e.currentTarget.dataset.result
      })
    },
    changeInvoiceType(e) {
      let invoiceType = 'invoiceInfo.invoiceType',
        riseType = 'invoiceInfo.riseType'
      let type = e.currentTarget.dataset.type
      if (type == 1) {
        this.setData({
          [invoiceType]: type,
          [riseType]: 1
        });
      } else {
        this.setData({
          [invoiceType]: type
        });
      }
    },
    changeHeadUpType(e) {
      let riseType = 'invoiceInfo.riseType'
      let type = e.currentTarget.dataset.type
      this.setData({
        [riseType]: type
      });
    },
    changeGetInvoiceType(e) {
      let acceptType = 'invoiceInfo.acceptType'
      let type = e.currentTarget.dataset.type
      this.setData({
        [acceptType]: type
      });
    },
    addressLoad() {
      // 地址
      let that = this;
      util.addressLoad(0, 0, function(res) {
        let list = [];
        util.addressLoad(1, res.data.data[0].code, function(res1) {
          util.addressLoad(2, res1.data.data[0].code, function(res2) {
            list = [res.data.data, res1.data.data, res2.data.data];
            that.setData({
              areaRange: list
            })
          })
        })
      })
    },
    // 选择地区
    bindRegionChange(e) {
      let that = this,
        list = that.data.areaRange,
        arr = e.detail.value,
        acceptAddress = 'invoiceInfo.acceptAddress';

      that.setData({
        areaRange: list,
        region: [list[0][arr[0]].name, list[1][arr[1]].name, list[2][arr[2]].name],
        [acceptAddress]: (list[0][arr[0]].name + list[1][arr[1]].name + list[2][arr[2]].name)
      });
    },
    columnChange(e) {
      let that = this;
      console.log("aaaa");
      console.log(e)
      let column = e.detail.column;
      let index = e.detail.value;
      let list = util.deepCopy(that.data.areaRange);
      let timer = "";
      clearTimeout(timer)
      timer = setTimeout(function() {
        if (column == 0) {
          util.addressLoad(1, list[0][index].code, function(res) {
            util.addressLoad(2, res.data.data[0].code, function(res1) {
              list[1] = res.data.data;
              list[2] = res1.data.data;
              that.setData({
                areaRange: list
              })
            })
          })
        } else if (column == 1) {
          util.addressLoad(2, list[1][index].code, function(res) {
            list[2] = res.data.data
            that.setData({
              areaRange: list
            })
          })
        }
      }, 500)

    },

    // 绑定数据
    inputEdit(e) {
      let name = e.currentTarget.dataset.name,
        value = e.detail.value;
      // 如果输入单位名称，则查询开票信息
      if (name == 'invoiceInfo.unitName') {
        this.unitNameFocus(e);
      }
      // 输入税号、银行卡号、单位电话不能输入汉字
      if (name == 'invoiceInfo.taxNum' || name == 'invoiceInfo.unitPhone' || name == 'invoiceInfo.bankNum') {
        value = value.replace(/[^\w\.\/\-]/ig, '');
      }
      this.setData({
        [name]: value
      });
    },

    unitNameFocus(e) {
      util.ajax(config.sellikeinvoicelist, {
        userId: app.userId,
        name: e.detail.value
      }, res => {
        if (res.data.code == 1) {
          let list = res.data.data,
            showList = [];
          list.forEach(x => {
            if (x.unitName) {
              showList.push(x)
            }
          })
          if (showList.length > 0) {
            this.setData({
              invoiceHistoryList: showList,
              showInvoiceHistory: true
            })
          }
        }
      })
    },

    unitNameBlur(e) {
      setTimeout(() => {
        this.setData({
          showInvoiceHistory: false
        })
      }, 300)
    },

    chooseInvoiceHistory(e) {
      let index = e.currentTarget.dataset.index,
        list = this.data.invoiceHistoryList,
        riseType = 'invoiceInfo.riseType',
        unitName = 'invoiceInfo.unitName',
        taxNum = 'invoiceInfo.taxNum',
        unitAddress = 'invoiceInfo.unitAddress',
        unitPhone = 'invoiceInfo.unitPhone',
        bankName = 'invoiceInfo.bankName',
        bankNum = 'invoiceInfo.bankNum',
        acceptType = 'invoiceInfo.acceptType',
        acceptAddress = 'invoiceInfo.acceptAddress',
        acceptAddressMore = 'invoiceInfo.acceptAddressMore',
        acceptName = 'invoiceInfo.acceptName',
        acceptPhone = 'invoiceInfo.acceptPhone';
      this.setData({
        [riseType]: list[index].riseType,
        [unitName]: list[index].unitName,
        [taxNum]: list[index].taxNum,
        [unitAddress]: list[index].unitAddress,
        [unitPhone]: list[index].unitPhone,
        [bankName]: list[index].bankName,
        [bankNum]: list[index].bankNum,
        [acceptType]: list[index].acceptType,
        [acceptAddress]: list[index].acceptAddress,
        [acceptAddressMore]: list[index].acceptAddressMore,
        [acceptName]: list[index].acceptName,
        [acceptPhone]: list[index].acceptPhone,
        showInvoiceHistory: false
      })
    },

    confirmInvoiceInfo(e) {
      let type = e.currentTarget.dataset.type;
      if (type == 'confirm') {
        // if (!this.data.invoiceInfo.mobile) {
        //   return util.showText('请填写接收手机号');
        // }
        // if (!this.data.invoiceInfo.email) {
        //   return util.showText('请填写接收邮箱');
        // }
        if (this.data.invoiceInfo.riseType == 1) {
          if (!this.data.invoiceInfo.unitName) {
            return util.showText('请填写单位名称');
          }
          if (!this.data.invoiceInfo.taxNum) {
            return util.showText('请填写税号');
          }
          if (this.data.invoiceInfo.invoiceType == 1) {
            if (!this.data.invoiceInfo.unitAddress) {
              return util.showText('请填写单位地址');
            }
            if (!this.data.invoiceInfo.unitPhone) {
              return util.showText('请填写单位电话');
            }
            if (!this.data.invoiceInfo.bankName) {
              return util.showText('请填写开户银行');
            }
            if (!this.data.invoiceInfo.bankNum) {
              return util.showText('请填写银行账号');
            }
          }
        }
        if (this.data.invoiceInfo.acceptType == 1) {
          if (!this.data.invoiceInfo.acceptAddress || !this.data.invoiceInfo.acceptAddressMore) {
            return util.showText('请完善邮寄地址');
          }
          if (!this.data.invoiceInfo.acceptName) {
            return util.showText('请填写收件人姓名');
          }
          if (!this.data.invoiceInfo.acceptPhone) {
            return util.showText('请填写收件人手机号');
          }
        }
      }
      this.triggerEvent('confirm', {
        invoiceInfo: this.data.invoiceInfo,
        // onlyClose: type == 'close' ? true : false
      });
    },
    preventD(e) {

    }
  }
})