/* cart/index/index.wxss */
@import '../../pages/template/goods-item/meal/meal.wxss';

.marker{display: none;}
.meal {height: auto;}
.meal .item{box-shadow:0 0 20rpx rgba(0, 0, 0, 0.3);}
.meal .title{display: none;}
.meal .item .title{display: block;}

.b-t-10{border-top:10rpx solid #f6f6f6;}
.page{position: relative;}
.con .item1{justify-content: space-between;align-items: center; font-size: 30rpx;color: #575757;background: #fff;padding: 24rpx;}
.item1 .default{font-size: 24rpx;color: #fff;background: linear-gradient(to right,#1794fd,#68ddff);height: 30rpx;width: 69rpx;display:inline-block;margin-right: 10rpx; border-radius: 15rpx;text-align: center;line-height: 30rpx; }
.item1 .name{color: #333333;margin-right: 20rpx;}
.item1 .info{margin: 10rpx 0;}
.item1 .address{font-size: 24rpx;color: #888;width: 85%;}
.item1 .right image{width: 22rpx;height: 44rpx;}

.item2{background: #fff;}
.we-item{border-bottom: 2rpx solid #e5e5e5;}
.we-title{font-size: 30rpx;color: #000;}
.we-desc{font-size: 28rpx;color: #999999;}
.item2 .tt{justify-content: space-between;padding: 10rpx 28rpx;font-size: 28rpx;font-weight: bold;}
.item2 .tt:nth-last-child(1){border-bottom: 2rpx dashed #e5e5e5;padding: 10rpx 0;width: 92%;margin: 0 auto;}

.words{background: #fff;font-size: 30rpx;color: #000;padding: 24rpx;padding-bottom: 140rpx;}
.words .wx-words{font-size: 24rpx;background: #f6f6f6;border-radius: 15rpx;}

.footer{position: fixed;bottom: 0;height: 87rpx;background: #e3f3ff;width: 100%;box-shadow: 0 -10rpx 10rpx rgba(100, 100, 100, 0.3);z-index: 1000;}
.footer .cont{font-size: 28rpx;height: 87rpx;padding: 0 28rpx;justify-content: space-between;}
.footer .cont .button{font-size: 30rpx;background: #ff2f43;color: #fff;padding: 5rpx 30rpx;border-radius: 50rpx}
.footer .cont .money{font-size: 24rpx;color: #ff2f43;margin-left: 10rpx;}
.footer .cont .price{font-size: 32rpx;color: #ff2f43;font-weight: bold;}
.price{font-size: 32rpx;}

@import "../../components/inputPwd/component.wxss";
/* 余额支付 */
/* components/inputPwd.wxss */
.deposit_pay{position: fixed;top: 0rpx;width: 100%;text-align: center;background: rgba(0,0,0,.6);padding-top: 200rpx;z-index: 1000;}
.deposit_pay_box{background: #fff;width: 560rpx;height: 526rpx;border-radius: 15rpx;margin: 0 auto;}
.deposit_pay_box .title{font-size: 32rpx;font-weight: bold;color: rgb(36,36,36);display: flex;align-items: center;height: 96rpx;border-bottom:1px solid #f5f5f5; }
.deposit_pay_box .title .close image{width: 34rpx;height: 34rpx;margin-left: 32rpx;}
.deposit_pay_box .title .text{margin-left: 28%;}
.deposit_pay_box .jine{font-size: 28rpx;color: rgb(36,36,36);width: 100%;text-align: center;margin-top: 34rpx;}
.deposit_pay_box .jine_num{width: 100%;display: flex;justify-content: center; text-align: center;margin-bottom: 20rpx;}
.deposit_pay_box .jine_num .icon{font-size: 40rpx;color: rgb(36,36,36);margin-top: 40rpx;font-weight: bold;}
.deposit_pay_box .jine_num .num{font-size: 78rpx;color: rgb(36,36,36);font-weight: bold;}
.deposit_pay_box .forget_pwd{width: 100%;text-align: right;font-size: 28rpx;color: rgb(73,90,160);margin-top: 40rpx;}
.deposit_pay_box .forget_pwd text{margin-right:32rpx; }


/*  */
.marker{display: none;}
.meal {height: auto;}
.meal .item{box-shadow:0 0 20rpx rgba(0, 0, 0, 0);width: 100%;padding:20rpx 35rpx;margin-top: 0rpx;}
.meal .title{display: none;}
.meal .item .title{display: block;}
.meal .item .marker{top: 20rpx;}
.backTop{bottom: 100rpx;}
.no-shopping {
  width: 100%;
  padding-top: 40%;
}
.item-clear {
  opacity: 0.7;
} 
.cart-rule{font-size: 22rpx;background: #f1f1f1;color: #aaa;padding: 5rpx 10rpx;border-radius: 10rpx;}
.avaliable-store {
  font-size: 24rpx;
  color: #e41436;
  
  /* position: absolute;
  top: 6rpx;
  left: 100rpx; */
  margin-left: 90rpx;
  margin-top: -6rpx;
}

.shopping-cart {
  /* margin: 0 auto;  */
  width: 100%;
  height: 300rpx;
  text-align: center;
  padding: 50rpx;
  box-sizing: border-box;
  color: #ccc;
}

.shopping-cart .icon {
  font-size: 120rpx;
}

.shopping-cart .text {
  font-size: 32rpx;
  margin-top: 10rpx;
}
/* pages/cart/cart.wxss */
.cartMain{
 margin-bottom: 200rpx;
 margin-top: 100rpx;
}
.cart_main{
  border-radius: 15rpx;
  /* width: 76%; */
  width: 100%;
  padding: 20rpx 35rpx;
  background: #fff;
}
.cart-image{
  width: 159rpx;
  height: 159rpx;
  padding: 15rpx 10rpx;
}
.cart-box{
  width: 100%;
  justify-content: flex-start;
  padding: 12rpx 0;
  /* margin-top: 15rpx; */
  /* padding-left: 60rpx; */
}
.cart-boxtwo{
  display: flex;
  flex-direction:column;
  
}
.left_box{overflow: hidden;width: 100%;}
.cart-right{
  padding: 0 10rpx;
  justify-content: space-between;
}
.money{font-size: 24rpx;}
.cart-title{
  font-size: 28rpx;
  display: -webkit-box;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp:2; 
  overflow: hidden;
  text-overflow:ellipsis;
}
.cart-price{
  display: flex;
  color: red;
  font-size: 32rpx;
  font-weight: bold;
}
.old-price{text-decoration: line-through;font-size: 26rpx;color: rgba(0, 0, 0, 0.6);margin-left: 10rpx;}
.old-price .money{font-size: 24rpx;}
.right{min-width: 67.5%;}
.right image{
  width: 50rpx;
  height: 50rpx;
  float: right;
  margin-right: 30rpx;
}
.cartNull{width: 200rpx;height: 200rpx;margin: 0 auto;}
.input{
  display: block;
  width: 65rpx;
  height: 65rpx;
 
  line-height: 65rpx;
  text-align: center;
  /* border: 1px solid red; */
}
.cart-reduce,.cart-add{
  width: 42rpx;
  height: 40rpx;
  font-size: 38rpx;
  border-radius: 50%;
}
.cart-reduce image,.cart-add image{width: 44rpx;height: 44rpx;margin: 0;}
.cart-add{
  border-left: none;
  background: #FBFBFB;
}
.cart-reduce{
  border-right: none;
  background: #FBFBFB;
}
.cart-text{
  min-width: 115rpx;
  font-size: 30rpx;
  text-align: right;
}
.cart-bottom{
  position: fixed;
  width: 100%;
  height: 87rpx;
  bottom:0;
  justify-content: space-between;
  background: #f7f7fa;
  box-shadow: 0 -5rpx 5rpx rgba(0, 0, 0, 0.2);
}
.cart-bottom .left{padding: 0 24rpx;}
.cart-bottom .all{width: 44rpx;height: 44rpx;margin-right: 10rpx;}
.yuan{
  display: block;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 1px solid red;
}
.icon{
  margin: 0rpx 20rpx;
}
.icon image{width: 44rpx;height: 44rpx;}
.SpCart{
  display: block;
  width: 300rpx;
  height: 300rpx;
}
.Sptext{
  font-size: 30rpx;
}
.cart-icon{
  margin:18rpx 20rpx;
  float: left;
}
.cart-sum{
  height: 100%;
  line-height: 87rpx;
  /* background: red; */
  float: right;
  margin-right: 30rpx;
  text-align: center;
}
.cart-pay{
  width: 65%;
  height: 100%;padding: 0 30rpx;
  justify-content: flex-end;
  display: flex;align-items: center;
}
.cart_pay{
  display: block;
  width: 135rpx;
  background: #ff2f43;
  border-radius: 50rpx; 
  line-height: 56rpx;
  text-align: center;
  font-size: 30rpx;
  color: white;
}
.sum_color{
  color: red;
  font-size: 30rpx;
  font-weight: bold;
}
.sum_text{
  font-size: 30rpx;
}
.checkAll{
  line-height: 87rpx;
  font-size: 28rpx;
}
.header{height: 80rpx;background: #fff;font-size: 30rpx;position: fixed;top: 0;width: 100%;background: linear-gradient(to right,#1794fd,#68ddff);line-height: 80rpx;color: #fff;box-shadow: 0 5rpx 5rpx rgba(0, 0, 0, 0.3);z-index: 1000;}
.header-box{padding: 0 24rpx;justify-content: space-between;}
.header-box view:nth-child(1){font-size: 22rpx;}

.del{display: none; color:#fff;font-size: 28rpx;height: 210rpx; line-height: 210rpx;text-align: center; width:120rpx; background:#ff2f43;}

/* 底部弹出层 */
.commodity_screen {
 width: 100%;
 height: 100%;
 position: fixed;
 top: 0;
 left: 0;
 background: #000;
 opacity: 0.5;
 overflow: hidden;
 z-index: 98;
 color: #fff;
}
.commodity_attr_box {
 width: 100%;
 overflow: hidden;
 position: fixed;
 bottom: 0;
 left: 0;
 z-index: 9999;
 background: #fff;
 height: 800rpx;
 padding: 30rpx 0;
}
.pay-title{
  color:#000;
  font-size: 30rpx;
  text-align: center;
}
.pay-title .bigMoney{font-size: 72rpx;color: #333333;font-weight: bold;}
.pay-title .smaMoney{font-size: 30rpx;color: #333333;min-height: 48rpx;}
.pay-title .smaMoney .line{text-decoration:line-through;}
.pay-type{
  height:110rpx;
  font-size: 30rpx;
  padding:0 30rpx;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pay-type .left{display: flex;}
.pay_card_img{
  width:36rpx;
  height:36rpx;
  margin-top: 10rpx;
  margin-left: 10rpx;
}
.shengyu{color: #444;font-size: 30rpx;}
.confirm-modal{
  width:100%;
  padding: 88rpx 0 50rpx 0;
  text-align: center;
}
.pay-confirm-btn{
  margin:0 auto;
  width:680rpx;
  height:80rpx;
  line-height: 80rpx;
}

.confirm-btn{
  width: 90%;
  line-height: 88rpx;
  height: 88rpx;
  border-radius:10rpx;
  color: #fff;
  font-size: 36rpx; 
  background: linear-gradient(to right,#1794fd,#68ddff);
}
.closeIcon{position: absolute;top: 30rpx;right: 50rpx;width: 50rpx;height: 50rpx;text-align: center;}
.closeIcon image{width: 30rpx;height: 30rpx;}