// components/activities-popup/actP.js
Component({
  properties: {
    visible:{
      type: Boolean,
      value: false,
      observer: "_visibleChange"
    },
    shareUrl:{
      type: String,
      value: ""
    }
  },
  data: {
    imgUri:'../..',
    show:false,
  },
  pageLifetimes:{
    show: function(){
		//接受参数
        var visible = this.data.visible;
		this._visibleChange(visible);
    }
  },
  
  methods: {
	  _visibleChange:function(){
	    var visible = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
	    this.setData({
	  	  show: visible
	    });
	  },
	  onHidden:function(){
		
		var that = this ;  
	  	
		that.setData({
			show:false,
		});
			  
		that.triggerEvent("hidden", {
		  visible: false
		});
		
	  },
      previewImage:function(e){
          var _shareUrl = this.data.shareUrl ;
          if(_shareUrl){
               wx.previewImage({
                    current: _shareUrl,  
                    urls: [_shareUrl]  
                }); 
          }
      }
  }
})