// components/payConfirmSelect/payConfirmSelect.js
var app = getApp();
Component({
  lifetimes: {
    attached: function() {
      let shopInfo = wx.getStorageSync('shopInfo');
      this.setData({
        bankCard: shopInfo.szmCStore.bankCard,
        openingBank: shopInfo.szmCStore.openingBank,
		companyTitle: shopInfo.szmCStore.companyTitle,
      })
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  /**
   * 组件的属性列表
   */
  properties: {
    showModalStatus: {
      type: Boolean,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
    payTypeList: {
      type: Array,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
    animationData: {
      type: Object,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
    first: {
      type: String,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
    second: {
      type: String,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
    third: {
      type: String,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
    total: {
      type: String,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    imgUri: app.imgUri,
    bankCard: "",
    openingBank: "",
	companyTitle: ""
  },

  /**
   * 组件的方法列表
   */
  methods: {
    hideModal() {
      this.triggerEvent('close', {});
    },
    payChange(e) {
      this.triggerEvent('change', {
        index: e.currentTarget.dataset.index
      });
    },
    payMoney() {
      this.triggerEvent('pay', {});
    }
  }
})