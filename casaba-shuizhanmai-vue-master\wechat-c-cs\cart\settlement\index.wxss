/* cart/index/index.wxss */
@import '../../pages/template/goods-item/meal/meal.wxss';

Page {
  --Colors: #ff2f2f;
  --fontSizeBase: 20;
}

.marker {
  display: none;
}

.meal {
  height: auto;
}

.meal .item {
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.3);
}

.meal .title {
  display: none;
}

.meal .item .title {
  display: block;
}

.b-t-10 {
  border-radius: 16rpx;
}

.page {
  position: relative;
}

.con .item1 {
  justify-content: space-between;
  align-items: center;
  font-size: var(--fontSizeBase+12)rpx;
  color: #575757;
  background: #fff;
  padding: 24rpx;
}

.item1 .default {
  font-size: var(--fontSizeBase+6)rpx;
  color: #fff;
  background: linear-gradient(to right, #1794fd, #68ddff);
  height: 30rpx;
  width: 69rpx;
  display: inline-block;
  margin-right: 10rpx;
  border-radius: 15rpx;
  text-align: center;
  line-height: 30rpx;
}

.item1 .customer-type {
  color: #fff;
  background: #3392fe;
  height: 30rpx;
  width: 69rpx;
  display: inline-block;
  margin-right: 16rpx;
  border-radius: 8rpx;
  text-align: center;
  line-height: 30rpx;
}

.item1 .name {
  color: #333;
  margin-right: 20rpx;
}

.item1 .info {
  margin: 10rpx 0;
  color: red;
}

.item1 .address {
  font-size: var(--fontSizeBase+4)rpx;
  color: #888;
  width: 85%;
}

.item1 .right image {
  width: 22rpx;
  height: 44rpx;
}

.item2 {
  background: #fff;
}

.we-item {
  border-bottom: 2rpx solid #e5e5e5;
}

.we-title {
  font-size: 30rpx;
  color: #000;
}

.we-desc {
  font-size: 28rpx;
  color: #999;
}

.item2 .tt {
  justify-content: space-between;
  padding: 4rpx 40rpx;
  font-size: 26rpx;
}

.words {
  border-radius: 16rpx;
  background: #fff;
  font-size: 30rpx;
  color: #000;
  padding: 12rpx 40rpx;
}

.words .wx-words {
  font-size: 26rpx;
}

.footer {
  position: fixed;
  bottom: 0;
  height: 120rpx;
  background: #fff;
  width: 100%;
  z-index: 1000;
}

.footer .cont {
  font-size: 28rpx;
  height: 120rpx;
  padding: 0 24rpx;
  justify-content: space-between;
}

.footer .cont .button {
  font-size: 40rpx;
  background: var(--Colors);
  color: #fff;
  border-radius: 50rpx;
  width: 180rpx;
  height: 74rpx;
  line-height: 74rpx;
  text-align: center;
  flex-shrink: 0;
}

.footer .cont .money {
  font-size: 30rpx;
  color: #ff2f43;
}

.footer .cont .price {
  font-size: 48rpx;
  color: #ff2f43;
  font-weight: bold;
}

.price {
  font-size: 24rpx;
}
@import "../../components/inputPwd/component.wxss";

/* 余额支付 */

/* components/inputPwd.wxss */

.deposit_pay {
  position: fixed;
  top: 0rpx;
  width: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
  padding-top: 200rpx;
  z-index: 1000;
}

.deposit_pay_box {
  background: #fff;
  width: 560rpx;
  height: 526rpx;
  border-radius: 15rpx;
  margin: 0 auto;
}

.deposit_pay_box .title {
  font-size: 32rpx;
  font-weight: bold;
  color: rgb(36, 36, 36);
  display: flex;
  align-items: center;
  height: 96rpx;
  border-bottom: 1px solid #f5f5f5;
}

.deposit_pay_box .title .close image {
  width: 34rpx;
  height: 34rpx;
  margin-left: 32rpx;
}

.deposit_pay_box .title .text {
  margin-left: 28%;
}

.deposit_pay_box .jine {
  font-size: 28rpx;
  color: rgb(36, 36, 36);
  width: 100%;
  text-align: center;
  margin-top: 34rpx;
}

.deposit_pay_box .jine_num {
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: center;
  margin-bottom: 20rpx;
}

.deposit_pay_box .jine_num .icon {
  font-size: 40rpx;
  color: rgb(36, 36, 36);
  margin-top: 40rpx;
  font-weight: bold;
}

.deposit_pay_box .jine_num .num {
  font-size: 78rpx;
  color: rgb(36, 36, 36);
  font-weight: bold;
}

.deposit_pay_box .forget_pwd {
  width: 100%;
  text-align: right;
  font-size: 28rpx;
  color: rgb(73, 90, 160);
  margin-top: 40rpx;
}

.deposit_pay_box .forget_pwd text {
  margin-right: 32rpx;
}

/*  */

.marker {
  display: none;
}

.meal {
  height: auto;
}

.meal .item {
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0);
  width: 100%;
  padding: 20rpx 35rpx;
  margin-top: 0rpx;
}

.meal .title {
  display: none;
}

.meal .item .title {
  display: block;
}

.meal .item .marker {
  top: 15rpx;
  height: 40rpx;
  line-height: 40rpx;
}

.meal .item .under {
  background: linear-gradient(to right, #aaa, #ccc);
}

.marker-new {
  position: absolute;
  left: 10rpx;
  background: linear-gradient(to right, #aaa, #ccc);
  width: 140rpx;
  color: #fff;
  font-size: 26rpx;
  text-align: center;
  border-radius: 10rpx;
  top: 15rpx;
  height: 40rpx;
  line-height: 40rpx;
}

.backTop {
  bottom: 100rpx;
}

.no-shopping {
  width: 100%;
  padding-top: 40%;
}

.item-clear {
  opacity: 0.7;
}

.cart-rule {
  font-size: 26rpx;
  color: #aaa;
  /* padding: 6rpx 0; */
  margin-bottom: 20rpx;
}

.avaliable-store {
  font-size: 24rpx;
  color: #e41436;
  margin-left: 90rpx;
  margin-top: -6rpx;
}

.shopping-cart {
  /* margin: 0 auto;  */
  width: 100%;
  height: 300rpx;
  text-align: center;
  padding: 50rpx;
  box-sizing: border-box;
  color: #ccc;
}

.shopping-cart .icon {
  font-size: 120rpx;
}

.shopping-cart .text {
  font-size: 32rpx;
  margin-top: 10rpx;
}

/* pages/cart/cart.wxss */

.cartMain {
  margin-bottom: 200rpx;
  margin-top: 100rpx;
}

.cart_main {
  border-radius: 15rpx;
  /* width: 76%; */
  width: 100%;
  padding: 20rpx 35rpx;
  background: #fff;
}

.cart-image {
  width: 159rpx;
  height: 159rpx;
  padding: 15rpx 20rpx 15rpx 0;
}

.cart-box {
  width: 100%;
  justify-content: flex-start;
  padding: 12rpx 10rpx;
  box-sizing: border-box;
  /* margin-top: 15rpx; *//* padding-left: 60rpx; */
}

.cart-boxtwo {
  display: flex;
  flex-direction: column;
}

.left_box {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.cart-right {
  justify-content: flex-end;
  width: 500rpx;
  align-items: center;
}

.money {
  font-size: 24rpx;
}

.cart-title {
  font-size: 28rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 40rpx;
  line-height: 40rpx;
  width: 500rpx;
}

.cart-price {
  display: flex;
  color: red;
  font-size: 34rpx;
  font-weight: bold;
}

.old-price {
  text-decoration: line-through;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.6);
  margin-left: 10rpx;
}

.old-price .money {
  font-size: 26rpx;
}

.right {
  width: 67.5%;
  height: 190rpx;
}

.right image {
  width: 50rpx;
  height: 50rpx;
  float: right;
  margin-right: 30rpx;
}

.cartNull {
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto;
}

.input {
  display: block;
  width: 65rpx;
  height: 65rpx;
  line-height: 65rpx;
  text-align: center;
  /* border: 1px solid red; */
}

.cart-reduce, .cart-add {
  width: 42rpx;
  height: 40rpx;
  font-size: 38rpx;
  border-radius: 50%;
}

.cart-reduce image, .cart-add image {
  width: 44rpx;
  height: 44rpx;
  margin: 0;
}

.cart-add {
  border-left: none;
  background: #fbfbfb;
}

.cart-reduce {
  border-right: none;
  background: #fbfbfb;
}

.cart-text {
  min-width: 115rpx;
  font-size: 30rpx;
  text-align: right;
}

.cart-bottom {
  position: fixed;
  width: 100%;
  height: 87rpx;
  bottom: 0;
  justify-content: space-between;
  background: #f7f7fa;
  box-shadow: 0 -5rpx 5rpx rgba(0, 0, 0, 0.2);
}

.cart-bottom .left {
  padding: 0 24rpx;
}

.cart-bottom .all {
  width: 44rpx;
  height: 44rpx;
  margin-right: 10rpx;
}

.yuan {
  display: block;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 1px solid red;
}

.icon {
  margin: 0rpx 20rpx;
}

.icon image {
  width: 44rpx;
  height: 44rpx;
}

.SpCart {
  display: block;
  width: 300rpx;
  height: 300rpx;
}

.Sptext {
  font-size: 30rpx;
}

.cart-icon {
  margin: 18rpx 20rpx;
  float: left;
}

.cart-sum {
  height: 100%;
  line-height: 87rpx;
  /* background: red; */
  float: right;
  margin-right: 30rpx;
  text-align: center;
}

.cart-pay {
  width: 65%;
  height: 100%;
  padding: 0 30rpx;
  justify-content: flex-end;
  display: flex;
  align-items: center;
}

.cart_pay {
  display: block;
  width: 135rpx;
  background: #ff2f43;
  border-radius: 50rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 30rpx;
  color: white;
}

.sum_color {
  color: red;
  font-size: 30rpx;
  font-weight: bold;
}

.sum_text {
  font-size: 30rpx;
}

.checkAll {
  line-height: 87rpx;
  font-size: 28rpx;
}

.header {
  height: 80rpx;
  background: #fff;
  font-size: 30rpx;
  position: fixed;
  top: 0;
  width: 100%;
  background: linear-gradient(to right, #1794fd, #68ddff);
  line-height: 80rpx;
  color: #fff;
  box-shadow: 0 5rpx 5rpx rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.header-box {
  padding: 0 24rpx;
  justify-content: space-between;
}

.header-box view:nth-child(1) {
  font-size: 22rpx;
}

.del {
  display: none;
  color: #fff;
  font-size: 28rpx;
  height: 210rpx;
  line-height: 210rpx;
  text-align: center;
  width: 120rpx;
  background: #ff2f43;
}

/* 底部弹出层 */

.commodity_screen {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
  overflow: hidden;
  z-index: 98;
  color: #fff;
}

.commodity_attr_box {
  width: 100%;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background: #fff;
  height: 800rpx;
  padding: 30rpx 0;
}

.pay-title {
  color: #000;
  font-size: 30rpx;
  text-align: center;
}

.pay-title .bigMoney {
  font-size: 72rpx;
  color: #333;
  font-weight: bold;
}

.pay-title .smaMoney {
  font-size: 30rpx;
  color: #333;
  min-height: 48rpx;
}

.pay-title .smaMoney .line {
  text-decoration: line-through;
}

.shengyu {
  color: #444;
  font-size: 30rpx;
}

.confirm-modal {
  width: 100%;
  padding: 88rpx 0 50rpx 0;
  text-align: center;
}

.pay-confirm-btn {
  margin: 0 auto;
  width: 680rpx;
  height: 70rpx;
  line-height: 70rpx;
}

.confirm-btn {
  width: 90%;
  line-height: 70rpx;
  height: 70rpx;
  border-radius: 36rpx;
  color: #fff;
  font-size: 30rpx;
  background: #3392fe;
}

.message-type {
  box-sizing: border-box;
  width: 100%;
  height: 4rpx;
  background-repeat: no-repeat;
  background-color: #fff;
  background-size: 100% 100%;
  overflow: hidden;
}

/* swtich整体大小 */

.wx-switch-input {
  width: 66rpx !important;
  height: 30rpx !important;
  margin-top: -10rpx;
}

/* false的样式 */

.wx-switch-input::before {
  width: 66rpx !important;
  height: 30rpx !important;
  background: #f2f2f2 !important;
}

/* true的样式 */

.wx-switch-input::after {
  width: 40rpx !important;
  height: 40rpx !important;
  margin-top: -6rpx;
}

.ya-tong-num {
  width: 76rpx;
  height: 34rpx;
  min-height: 34rpx;
  background: #f2f2f2;
  text-align: center;
  display: flex;
  align-items: center;
  margin-top: 3rpx;
}

.reduce-add-button {
  width: 50rpx;
}

.receipt {
  border-radius: 16rpx;
  padding: 12rpx 40rpx;
}

.invoice_information_box {
  width: 100%;
  overflow: hidden;
  border-radius: 16rpx 16rpx 0 0;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background: #fff;
  height: 800rpx;
  padding: 30rpx 0;
}

.pay-type {
  min-height: 50rpx;
  font-size: 30rpx;
  padding: 30rpx 10rpx;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pay-type .left {
  display: flex;
}

.pay_card_img {
  width: 36rpx;
  height: 36rpx;
}

.pay-type-label {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.close {
  width: 34rpx;
  height: 34rpx;
  background: #f5f5f5;
  border-radius: 50%;
}

.address-desc{
	display: flex;
	justify-content: flex-end ;
	font-size: 28rpx;
	margin-right: 60rpx;
	margin-bottom: 10rpx;
	margin-top: 10rpx;
}
.coupon-box {
  width: 100%;
  height: auto;
  overflow: hidden;
  background: #fff;
}

.coupon-box .coupon-item {
  width: 100%;
  height: 108.3rpx;
  overflow: hidden;
  background: #fff;
  display: flex;
  padding-left: 31.25rpx;
}

.coupon-box .l {
  flex: 1;
  height: 43rpx;
  line-height: 43rpx;
  padding-top: 35rpx;
}

.coupon-box .l .name {
  float: left;
  font-size: 30rpx;
  color: black;
}

.coupon-box .l .txt {
  float: right;
  font-size: 30rpx;
  color: black;
}

.coupon-box .r {
  margin-top: 15.5rpx;
  width: 77rpx;
  height: 77rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.coupon-box .r image {
  width: 52.078rpx;
  height: 52.078rpx;
}

.shadowbg {
	position: fixed;
	left: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, .5);
  z-index: 99999;
}

.noticebg {
	width: 100vw;
	height: 1000rpx;
	position: fixed;
	top: 56%;
	/* left: -3%; */
	transform: translate(0, -50%);
	background: url('https://waterstation.com.cn/szm/szmc/images/messageInform.png');
	background-size: 100% 100%;
}
.noticebg11 {
	width: 100vw;
	height: 1000rpx;
	position: fixed;
	top: 56%;
	transform: translate(0, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.noticecontent {
	padding: 0 130rpx;
	color: 32rpx;
	color: #FFFFFF;
	font-weight: 400;
	line-height: 40rpx;
	margin-top: 260rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 10;
	overflow: hidden;
}

.cart-bottom1 {
  position: fixed;
  width: 100%;
  height: 60rpx;
  bottom: 120rpx;
  justify-content: space-between;
  background: white;
  z-index: 10;
}
