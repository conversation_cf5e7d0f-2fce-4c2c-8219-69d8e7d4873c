// cart/index/index.js 
import {
  windowHeight,
  windowWidth,
} from '../../utils/common.js';
import config from '../../config.js';
import util from '../../utils/util.js';
var app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    windowHeight: windowHeight(),
    showModalStatus: false,
    // 输入框参数设置
    inputData: {
      input_value: "", //输入框的初始内容
      value_length: 0, //输入框密码位数
      isNext: false, //是否有下一步的按钮
      get_focus: true, //输入框的聚焦状态
      focus_class: true, //输入框聚焦样式
      value_num: [1, 2, 3, 4, 5, 6], //输入框格子数
      height: "84rpx", //输入框高度
      width: "504rpx", //输入框宽度
      see: false, //是否明文展示
      interval: true, //是否显示间隔格子

      discountFlag: 0, //是否有水票可用
    },
    is_depositPay: 0, //是否显示密码输入框
    sexBox: ["余额支付", "微信支付", '货到付款', '月付'],
    payType: "余额支付",
    is_info: false,
    moreMeal: false, //推荐套餐模板 是否显示更多按钮
    meal: [],
    mealRe: [],
    is_showCar: 'num', // num 数量-结算页 car 购物车- liji 立即抢购-首页
    // 总价 total
    total: '',
    goodsTotal: "", // goodsTotal 商品总价 
    groomsTotal: "", // 套餐总价
    sureUseTicket: '', //使用的水票
    // 运费 freight
    freight: 0,
    useTicketNum: 0,
    useTicketNumTitle: '请选择您要使用的水票',
    cartIds: [],
    is_submit: true,
    totalNum: 0, //总数量
    is_blur: 0,
    onFocus: false,
    deduct: '', // 派单员提成 
    isUserTicket: 1,
    cartItems: '',
    usableTicket: 1,
    freightAndUpfloor: 0,
    isShowYF: false, //是否显示月付
    imgUri: app.imgUri,
    // 新手蒙版指引
    showMaskGuide: false,
    // 点击显示下一张图片
    showGuideImage: 1,
    random: 0,
    szmCMaskId: '',// 蒙层Id
  },
  // // 获取上楼费 getSelupprice
  // getSelupprice(e){
  //   var _this = this;
  //   app.request({
  //     url: config.getSelupprice,
  //     data:{
  //       userId:app.userId,
  //     },
  //     success(res){
  //       if(res.data.code == 1){
  //         _this.setData({
  //           upPrice:res.data.data
  //         })
  //         //console.log(_this.data.upPrice, 'wwwww')
  //       }else{
  //         _this.setData({
  //           upPrice:0
  //         })
  //         wx.showToast({
  //         title: res.data.data,
  //         icon:'none'
  //       })}
  //     }
  //   })

  // },
  goSettlement(e) {
    var tarType = e.currentTarget.dataset.id;
    var that = this;
    var sureUseTicket = that.data.sureUseTicket;
    var cartItems = that.data.cartItems;
    var o = [],
      is_info = that.data.is_info;
    if (!is_info) {
      wx.showModal({
        title: '温馨提示',
        content: '请先添加收货信息，点击确定前往设置！',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/user/address/address?pageType=set',
            })
          }
        }
      })
      return;
    }
    var login = wx.getStorageSync('login'),
      payType = that.data.payType;
    for (var i in cartItems) {
      var item = '';
      if (cartItems[i].selected == true) {
        if (cartItems[i].source == 0) {
          item = {
            'skuId': cartItems[i].cartShop.skuId,
            'state': cartItems[i].source
          }
        } else if (cartItems[i].source == 1) {
          item = {
            'skuId': cartItems[i].cartShopGroupList.groupId,
            'state': cartItems[i].source
          }
        } else {
          item = {
            'skuId': cartItems[i].cartShop.skuId,
            'state': cartItems[i].source
          }
        }
        o.push(item);
      }
    }
    if (that.data.payType!='月付'){
      if (Number(login[0].money) < Number(that.data.total)) { // 判断余额是否充足
        that.setData({
          isbalance: 0,
          payType: '微信支付'
        })
      } else {
        that.setData({
          isbalance: 0,
          payType: '微信支付'
        })
      }
      if (sureUseTicket.list) {
        if (sureUseTicket.list.length > 0) {
          that.setData({
            isUserTicket: 0,
            payType: "微信支付"
          })
        }
      }
    }

    var animation = wx.createAnimation({ // 显示遮罩层
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation
    animation.translateY(300).step()
    that.setData({
      animationData: animation.export(),
    })
    setTimeout(function() {
      animation.translateY(0).step()
      that.setData({
        animationData: animation.export(),
        showModalStatus: true
      })
    }.bind(that), 200);
    // wx.request({
    //   url: config.selectskuall,
    //   data:o,
    //   method:'POST',
    //   success(res){
    //     if(res.data.code == 1){

    //     }else{
    //       wx.showModal({
    //         title: '温馨提示',
    //         content: '您有'+res.data.data+'件商品已下架或库存不足，请返回购物车查看！',
    //         showCancel:false,
    //         success(r){
    //           if(r.confirm){
    //             wx.switchTab({
    //               url: '/pages/cart/cart',
    //             })
    //           }
    //         }
    //       })
    //     }
    //   }
    // })

  },
  hideModal: function() {
    // 隐藏遮罩层
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    this.animation = animation
    animation.translateY(300).step()
    this.setData({
      animationData: animation.export(),
    })
    setTimeout(function() {
      animation.translateY(0).step()
      this.setData({
        animationData: animation.export(),
        showModalStatus: false
      })
    }.bind(this), 200)
  },

  onShowText(e) {
    this.setData({
      is_blur: 0,
      onFocus: false,
    })
  },
  onShowTextarea(e) {
    this.setData({
      is_blur: 1,
      onFocus: true,
    })
  },
  forgetPwd(e) {
    wx.navigateTo({
      url: '/user/mySet/setKeyt/setKeyt',
    })
  },
  selectSex() {
    let that = this;
    wx.showActionSheet({
      itemList: that.data.sexBox,
      success: function(res) {
        if (!res.cancel) {
          that.setData({
            payType: that.data.sexBox[res.tapIndex]
          })
          if (that.data.useTicketNum != 0) {
            if (res.tapIndex == 2 || res.tapIndex == 3) {
              wx.showModal({
                title: '温馨提示',
                content: '货到付款和月付暂不支持使用水票，点击确定继续使用货到付款或月付',
                success(res) {
                  if (res.confirm) {
                    that.setData({
                      useTicketNum: 0,
                      useTicketNumTitle: '货到付款和月付暂不支持水票',
                      discount: 0,
                      total: that.data.goodsTotal + that.data.groomsTotal + Number(that.data.freight)
                    })
                  } else {
                    that.setData({
                      payType: '余额支付'
                    })
                  }
                }
              })
            }
          }
        }
      }
    });
  },
  // 当组件输入数字6位数时的自定义函数
  valueSix(e) {
    var _this = this;
    var code = e.detail;
    _this.setData({
      is_submit: false,
    })
    var login = wx.getStorageSync('login');
    var shopInfo = wx.getStorageSync('shopInfo');
    var userId = app.userId;
    var freight = shopInfo.szmCStore.storeShippingfee == null ? 0 : shopInfo.szmCStore.storeShippingfee;
    //console.log(_this.data.addressInfo, "我是地址")

    wx.request({
      url: config.walletPayGoods,
      data: {
        "payCode": code, //支付密码 
        "price": Number(_this.data.total).toFixed(2), // 实际付款 
        orderNum: _this.data.walletPay,
        userId: app.userId,
      },
      method: "POST",
      success(res) {
        //console.log(res);
        if (res.data.code == 1) {
          wx.showToast({
            title: '支付中...',
          })
          wx.removeStorageSync('sureUseTicket');
          wx.removeStorageSync('ticketList');
          setTimeout(function() {
            wx.redirectTo({
              url: '/cart/paySuccess/paySuccess',
              success(res) {
                _this.setData({
                  is_submit: true,
                })
              }
            })
          }, 500)
        } else {
          wx.showToast({
            title: res.data.data,
            icon: 'none'
          })
        }
      },
      fail(res) {

      }
    })
    //console.log(code);
  },
  // 关闭押金支付密码输入框
  closeDepositPay: function() {
    var _this = this;
    wx.showToast({
      title: '支付已取消',
      icon: 'none',
      success(res) {
        setTimeout(function() {
          _this.setData({
            is_depositPay: 0
          })
          wx.redirectTo({
            url: '/user/myOrder/myOrder',
          })
        }, 300)
      }
    })
  },
  // // 去结算
  payMoney: function(e) {
    var _this = this;
    var payType = _this.data.payType;
    var login = wx.getStorageSync('login');
    if (_this.data.addressInfo == undefined || _this.data.addressInfo == '') {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      })
      return;
    }
    _this.setData({
      showModalStatus: false
    })

    app.request({
      url: config.deduct,
      data: {
        storeId: app.storeId,
        addressId: _this.data.addressInfo.addressId,
      },
      success(res) {
        if (res.data.code == 1) {
          _this.setData({
            deduct: res.data.data
          })
          if (_this.data.is_submit) {
            if (payType == '余额支付') {
              _this.unOnlinePay(4);
              //console.log(_this.data.inputData);
            } else if (payType == '微信支付') {
              _this.unOnlinePay(1)
            } else if (payType == '货到付款') {
              // unOnlinePay 
              _this.unOnlinePay(2);
            } else if (payType == '月付') {
              _this.unOnlinePay(3);
            }
          }
        } else {
          wx.showToast({
            title: res.data.data,
            icon: 'none'
          })
        }
      }
    })

  },

  // 线下支付 货到付款 & 月付
  unOnlinePay(payId) {
    var _this = this;
    _this.setData({
      is_submit: false,
    })
    var login = wx.getStorageSync('login');
    var shopInfo = wx.getStorageSync('shopInfo');
    var userId = app.userId;
    var freight = shopInfo.szmCStore.storeShippingfee;
    //console.log(shopInfo);
    var uri = '';
    if (payId == 1) {
      if (Number(_this.data.total) == 0) {
        uri = config.unOnlinePay;
        payId = 5
      } else {
        uri = config.wxchatPay;
        payId = payId
      }
    } else if (payId == 4) { // 余额支付
      if (login[0].paymentCode == null) {
        wx.showModal({
          title: '温馨提示',
          content: '您尚未设置钱包密钥，点击确定前往个人中心设置或选择其他支付方式！',
          success(res) {
            if (res.confirm) {
              wx.navigateTo({
                url: '/user/mySet/setKeyt/setKeyt',
              })
            }
          }
        })
        return;
      }
      if (Number(login[0].money) < Number(_this.data.total)) {
        wx.showModal({
          title: '温馨提示',
          content: '您的钱包余额不足，请前往个人中心充值或选择其他支付方式！',
          success(res) {
            if (res.confirm) {
              wx.navigateTo({
                url: '/user/mySet/setKeyt/setKeyt',
              })
            }
          }
        })
        return;
      }
      uri = config.unOnlinePay;
    } else {
      uri = config.unOnlinePay;
    }
    wx.request({
      url: uri,
      data: {
        "upPrice": _this.data.upPrice, //上楼费 
        "cartIds": _this.data.cartIds, //购物车id array
        "createIden": userId, //用户ID str
        "userId": userId, //用户ID 
        "freightPayable": freight == null ? 0 : freight, //运费 
        "incomeAddrId": _this.data.addressInfo.addressId, //收货id
        "userAddress": _this.data.addressInfo.province + _this.data.addressInfo.city + _this.data.addressInfo.area + _this.data.addressInfo.street + (_this.data.addressInfo.r1 == 0 ? " 个人·" : ' 企业·') + (_this.data.addressInfo.r2 == 0 ? "非电梯房·" : '电梯房·') + (_this.data.addressInfo.r3 == 8 ? '7楼以上' : _this.data.addressInfo.r3 + '楼 ') + (_this.data.addressInfo.r4 == null ? '' : _this.data.addressInfo.r4), // 用户地址
        "userContent": _this.data.remarks, // 用户留言
        "userName": _this.data.addressInfo.userName, // 用户姓名
        "userPhone": _this.data.addressInfo.telphoneOne, // 用户电话
        "orderDiscounts": Number(_this.data.discount), // 优惠价格
        "orderMoney": Number(_this.data.total) + Number(_this.data.discount), // 订单原价
        "paymentModeId": payId, // 付款方式id 
        "r1": Number(_this.data.total), // 实际付款 
        "r2": app.storeId, // 商户id
        "r4": JSON.stringify(_this.data.r4Data), // 优惠方式
        "r5": _this.data.totalNum, // 总数量
        "smzCGroupOrderList": _this.data.smzCGroupOrderList, // 套餐列表
        "smzCOrderDetailsList": _this.data.smzCOrderDetailsList, // 普通商品 列表
      },
      method: "POST",
      success(res) {
        if (res.data.code == 1) {
          if (payId == 1) {
            wx.requestPayment({
              timeStamp: res.data.data.timeStamp,
              nonceStr: res.data.data.nonceStr,
              package: res.data.data.package,
              signType: res.data.data.signType,
              paySign: res.data.data.paySign,
              success(data) {
                //console.log(data, 'data');
                if (data.errMsg == "requestPayment:ok") {
                  wx.showToast({
                    title: '支付中...',
                  })
                  wx.removeStorageSync('sureUseTicket');
                  wx.removeStorageSync('ticketList');
                  setTimeout(function() {
                    wx.redirectTo({
                      url: '/cart/paySuccess/paySuccess',
                      success(res) {
                        _this.setData({
                          is_submit: true,
                        })
                      }
                    })
                  }, 500)
                }
              },
              fail(f) {
                //console.log(f, 'f');
                if (f.errMsg == "requestPayment:fail cancel") {
                  wx.showToast({
                    title: '取消支付',
                    icon: "none",
                  })
                  setTimeout(function() {
                    wx.redirectTo({
                      url: '/user/myOrder/myOrder',
                      success(res) {
                        _this.setData({
                          is_submit: true,
                        })
                      }
                    })
                  }, 500)
                }
              },
            })
          } else if (payId == 4) {
            var inputData = 'inputData.get_focus',
              res = res.data.data
            _this.setData({
              is_depositPay: 1,
              [inputData]: true,
              walletPay: res
            });
          } else {
            wx.showToast({
              title: '支付中...',
            })
            setTimeout(function() {
              wx.redirectTo({
                url: '/cart/paySuccess/paySuccess',
                success(res) {
                  _this.setData({
                    is_submit: true,
                  })
                }
              })
            }, 500)
          }
        } else {
          wx.showToast({
            title: res.data.data,
          })
        }
      },
      fail(res) {

      },
    })
  },
  getRemarks(e) {
    //console.log(e);
    var remarks = e.detail.value;
    this.setData({
      remarks: remarks
    })
  },
  payChange(e) {
    //console.log(e);
    var _this = this,
      payType = e.currentTarget.dataset.payType;
    //console.log(payType)
    _this.setData({
      payType: payType
    })
  },
  selectTicket(e) {
    var _this = this;
    var id = [];
    var cartItems = wx.getStorageSync('cartItems');
    for (var i in cartItems) {
      if (cartItems[i].selected && cartItems[i].cartShop != null) {
        id.push(cartItems[i].cartShop.skuId);
      }
    }
    if (_this.data.isCanTarget == 1) {
      if (_this.data.discount > 0) {
        if (util.judgeModel('spdk') == 1) {
          wx.navigateTo({
            url: '/cart/waterTicket/waterTicket?idArr=' + JSON.stringify(id) + '&total=' + Number(Number(_this.data.total) + Number(_this.data.discount))
          })
        } else {
          wx.navigateTo({
            url: '/cart/waterTicketOld/waterTicketOld?idArr=' + JSON.stringify(id) + '&total=' + Number(Number(_this.data.total) + Number(_this.data.discount))
          })
        }
      } else {
        if (util.judgeModel('spdk') == 1) {
          wx.navigateTo({
            url: '/cart/waterTicket/waterTicket?idArr=' + JSON.stringify(id) + '&total=' + _this.data.total
          })
        } else {
          wx.navigateTo({
            url: '/cart/waterTicketOld/waterTicketOld?idArr=' + JSON.stringify(id) + '&total=' + _this.data.total
          })
        }
      }
    } else {
      wx.showModal({
        title: '温馨提示',
        content: '您暂无可用的优惠方式，可能您没有购买水票或者您购买的商品暂时不支持使用水票！',
        showCancel: false
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      random: Math.ceil(Math.random() * 100000)
    })
  },
  // 请选择个人信息
  chooseInfo: function(e) {
    wx.navigateTo({
      url: '/user/address/address?pageType=set',
    })
  },
  // 去 商家服务

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  // 优惠方式提示
  useDiscount() {
    let that = this;
    var idBox = [];
    var cartItems = wx.getStorageSync('cartItems');
    for (let i in cartItems) {
      if (cartItems[i].selected && cartItems[i].cartShop != null) {
        idBox.push(cartItems[i].cartShop.skuId);
      }
      if (cartItems[i].cartShop != null && cartItems[i].selected == true && (cartItems[i].cartShop.newSource == 0 || cartItems[i].cartShop.newSource == 3 )) {
        // newSource //0普通1限时2买赠3优惠
        wx.request({
          url: config.isHaveTicket,
          data: {
            productModelId: idBox,
            userId: app.userId,

            storeId: app.storeId
          },
          method: "POST",
          success(res) {
            if (res.data.code == 1) {
              //console.log(res);
              let list = res.data.data;
              let num = 0;
              list.forEach(function(item) {
                // if (item.isUsable == 1){
                num = num + Number(item.count);
                // } 
              })
              if (num) {
                that.setData({
                  useTicketNumTitle: num + "张可用",
                  discountFlag: 1,
                  isCanTarget: 1,
                })
              }
            }
          },
        })
      } else {
        that.setData({
          useTicketNumTitle: '暂无可用的优惠方式',
          discountFlag: 0,
          isCanTarget: 0,
        })
      }
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    var _this = this;
    _this.useDiscount();
    app.getToken();
    var login = wx.getStorageSync('login');
    if (login == null || login == '' || login == undefined) {
      app.getToken();
    } else {
      _this.setData({
        login: login
      })
    };
    if (!_this.data.addressInfo) {
      _this.defaultAddress();
    }
    // _this.selectallbyuseridMaskGuide();

    wx.removeStorageSync('ticketList')

    var cartItems = wx.getStorageSync('cartItems'),
      login = wx.getStorageSync('login'),
      goodsTotal = 0, // 普通商品总价
      goodsTotalOriginal = 0, // 普通商品原价
      groomsTotal = 0, //套餐数据总价
      groomsTotalOriginal = 0, //套餐数据总价
      smzCGroupOrderList = [], //套餐数据
      smzCOrderDetailsList = [], // 普通商品
      cartIds = [], // 购物车 id total
      totalNum = 0; //结算总数量 
    var isOnlyGroup = 1,
      isOnlyDiscouts = 1;
    var shopInfo = wx.getStorageSync('shopInfo');
    if (shopInfo != null || shopInfo != '' || shopInfo != undefined) {
      _this.setData({
        freightAndUpfloor: Number(Number(shopInfo.szmCStore.storeShippingfee) + Number(login[0].upFloorMoney)).toFixed(2)
      })
    }
    //console.log(shopInfo);
    // cartShopGroupList cartShop
    for (var i in cartItems) {
      if (cartItems[i].selected) {
        console.log(cartItems)
        cartIds.push(cartItems[i].cartId);
        if (cartItems[i].cartShop == null && cartItems[i].cartShopGroupList.skuNumber != '商品数量不足' && cartItems[i].cartShopGroupList.groupState == 0) {
          isOnlyDiscouts = 0;
          smzCGroupOrderList.push({
            groupOrderProductPrice: cartItems[i].cartShopGroupList.rulingPrice * cartItems[i].prouderNum,
            orderProductNum: cartItems[i].prouderNum,
            shopgroupId: cartItems[i].cartShopGroupList.groupId
          })
          groomsTotal += cartItems[i].cartShopGroupList.rulingPrice * cartItems[i].prouderNum;
          groomsTotalOriginal += cartItems[i].cartShopGroupList.originalPrice * cartItems[i].prouderNum;
          totalNum += cartItems[i].prouderNum;
        } else if (cartItems[i].cartShopGroupList == null && cartItems[i].cartShop.skuNumber != '商品数量不足' && cartItems[i].cartShop.skuState == 0) {
          isOnlyGroup = 0;
          if (cartItems[i].cartShop.vipPrice != null) {
            isOnlyDiscouts = 1;
            // 0普通1限时2买赠3优惠
            smzCOrderDetailsList.push({
              orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice,
              orderProductNum: cartItems[i].prouderNum,
              productModelId: cartItems[i].cartShop.skuId,
              productSkuimg: cartItems[i].cartShop.skuPicture,
              productSkuname: cartItems[i].cartShop.skuName,
              r2: cartItems[i].source,
              source: cartItems[i].cartShop.newSource,
            })
            goodsTotal += cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice;
            goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
          } else {
            smzCOrderDetailsList.push({
              orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].prouderPrice,
              orderProductNum: cartItems[i].prouderNum,
              productModelId: cartItems[i].cartShop.skuId,
              productSkuimg: cartItems[i].cartShop.skuPicture,
              productSkuname: cartItems[i].cartShop.skuName,
              r2: cartItems[i].source,
              source: cartItems[i].cartShop.newSource,
            })
            goodsTotal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
            goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
          }
          totalNum += cartItems[i].prouderNum;
        }

      }
    }
    console.log(isOnlyGroup, '是否只有套餐')
    console.log(isOnlyDiscouts, '是否只有普通商品')
    this.setData({
      isOnlyGroup: isOnlyGroup, // 是否只有套餐
      isOnlyDiscouts: isOnlyDiscouts, // 是否只有优惠
      cartItems: cartItems,
      goodsTotal: Number(goodsTotal).toFixed(2),
      goodsTotalOriginal: Number(goodsTotalOriginal).toFixed(2),
      goodsTotalDiscounts: Number(Number(goodsTotalOriginal) - Number(goodsTotal)).toFixed(2),
      groomsTotal: Number(groomsTotal).toFixed(2),
      groomsTotalOriginal: Number(groomsTotalOriginal).toFixed(2),
      groomsTotalDiscounts: Number(Number(groomsTotalOriginal) - Number(groomsTotal)).toFixed(2),
      cartIds: cartIds,
      totalNum: totalNum,
      upPrice: login[0].upFloorMoney || 0,
      total: Number(goodsTotal) + Number(groomsTotal) + Number(shopInfo.szmCStore.storeShippingfee) + Number(login[0].upFloorMoney),
      balance: login[0].money,
    })


    if (util.judgeModel('spdk') == 0) {
      var sureUseTicket = _this.data.sureUseTicket; // 已选择水票的集合
      console.log(sureUseTicket, '我是老版本的');
      // 提前处理选中的水票  将modelid一致的规成一个数组
      var discount = 0; // 优惠总额
      var useTicketNum = 0; // 使用的水票数量
      var r4Data = []; // 结算提交--水票使用
      for (var i in cartItems) {
        for (var j in sureUseTicket) {
          if (cartItems[i].cartShop != null) {
            if (cartItems[i].cartShop.skuId == sureUseTicket[j].productModelId) {

              if (cartItems[i].prouderNum <= sureUseTicket[j].count) { // 如果结算的商品数量 小于等于 水票的数量

                useTicketNum += cartItems[i].prouderNum;

                if (cartItems[i].cartShop.vipPrice != null) {
                  discount += cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice;
                } else {
                  discount += cartItems[i].prouderNum * cartItems[i].prouderPrice;
                }


                if (Number(Number(sureUseTicket[j].count) - Number(sureUseTicket[j].discountsNum)) >= Number(useTicketNum)) { // 使用总数量 小于 水票购买的数量 全是买的
                  r4Data.push({
                    "waterId": sureUseTicket[j].relevancesId,
                    "number": cartItems[i].prouderNum,
                    "waterName": sureUseTicket[j].specification,
                    "waterMoney": sureUseTicket[j].face,
                    "state": 1,
                    "allPrice": Number(cartItems[i].prouderNum) * Number(sureUseTicket[j].face),
                    "deductNum": 0, // 买的水票抵扣的数量
                    "discountsNum": 0, // 赠的水票抵扣的数量
                    "num": cartItems[i].prouderNum, // 买的兑换的数量
                    "discountsNumWater": 0 // 赠的水票兑换的数量
                  })
                } else { // 部分买的 部分赠的
                  r4Data.push({
                    "waterId": sureUseTicket[j].relevancesId,
                    "number": cartItems[i].prouderNum,
                    "waterName": sureUseTicket[j].specification,
                    "waterMoney": sureUseTicket[j].face,
                    "state": 1,
                    "allPrice": Number(cartItems[i].prouderNum) * Number(sureUseTicket[j].face),
                    "deductNum": 0,
                    "discountsNum": 0,
                    "num": Number(sureUseTicket[j].count) - Number(sureUseTicket[j].discountsNum),
                    "discountsNumWater": Number(useTicketNum) - (Number(sureUseTicket[j].count) - Number(sureUseTicket[j].discountsNum))
                  })
                }

                cartItems[i].prouderNum = cartItems[i].prouderNum - cartItems[i].prouderNum


                // r4Data.push({ waterId: sureUseTicket[j].relevancesId, number: cartItems[i].prouderNum })
              } else { // 如果结算的商品数量 大于 水票的数量 
                // 
                useTicketNum += sureUseTicket[j].count;



                if (cartItems[i].cartShop.vipPrice != null) {
                  discount += sureUseTicket[j].count * cartItems[i].cartShop.vipPrice;
                } else {
                  discount += sureUseTicket[j].count * cartItems[i].prouderPrice;
                }

                r4Data.push({
                  "waterId": sureUseTicket[j].relevancesId,
                  "number": sureUseTicket[j].count,
                  "waterName": sureUseTicket[j].specification,
                  "waterMoney": sureUseTicket[j].face,
                  "state": 1,
                  "allPrice": Number(cartItems[i].prouderNum) * Number(sureUseTicket[j].face),
                  "deductNum": 0,
                  "discountsNum": 0,
                  "num": useTicketNum - Number(sureUseTicket[j].discountsNum),
                  "discountsNumWater": Number(sureUseTicket[j].discountsNum)
                })

                cartItems[i].prouderNum = cartItems[i].prouderNum - sureUseTicket[j].count

              }
            }
          }
        }
      }
    } else {
      var sureUseTicket = wx.getStorageSync('sureUseTicket'); // 已选择水票的集合
      //console.log(sureUseTicket, '我是选择的水票')
      var discount = sureUseTicket.allDiscount || 0; // 优惠总额
      var undiscount = 0; // 未绑定优惠总额
      var useTicketNum = sureUseTicket.allNumber || 0; // 已绑定使用的水票数量
      var unuseTicketNum = 0; // 未绑定使用的水票数量
      var r4Data = ''; // 结算提交--水票使用
      r4Data = sureUseTicket.list
    }

    // if (login[0].money < _this.data.total) { // 判断余额是否充足
    //   // //console.log(1222222222222222222)
    //   _this.setData({
    //     isbalance: 0,
    //     payType: '微信支付'
    //   })
    // } else {
    //   _this.setData({
    //     isbalance: 1,
    //     payType: '余额支付'
    //   })
    // }

    _this.setData({
      discount: Number(discount).toFixed(2),
      total: Number(_this.data.total - discount).toFixed(2),
      oldTotal: Number(_this.data.total).toFixed(2),
      useTicketNum: useTicketNum,
      r4Data: r4Data,
      sureUseTicket: sureUseTicket,
      smzCGroupOrderList: smzCGroupOrderList,
      smzCOrderDetailsList: smzCOrderDetailsList,
      isShowYF: util.judgeModel('yf'),
      payType: util.judgeModel('yf')?"月付":"微信支付"
    })

  },
  defaultAddress() {
    var _this = this;
    // 查询默认地址 defaultAddress
    app.request({
      url: config.defaultAddress,
      data: {
        userId: app.userId,
      },
      success(res) {
        //console.log(res);
        if (res.data.code == 1) {
          _this.setData({
            is_info: true,
            addressInfo: res.data.data
          })
        } else {
          _this.setData({
            is_info: false,
            addressInfo: ''
          })
        }
      },
    })
  },
  showNextImage(e) {
    let showGuideImage = this.data.showGuideImage;
    this.setData({
      showGuideImage: showGuideImage + 1
    });
  },
  showNextImageLast(e) {
    let _this = this;
    app.request({
      url: config.updatemaskbyid,
      data: {
        maskId: _this.data.szmCMaskId
      },
      success(res) {
        if (res.data.code == 1) {
          _this.setData({
            showMaskGuide: false,
          })
        }
      }
    })
  },
  selectallbyuseridMaskGuide() {
    let _this = this;
    app.request({
      url: config.selectallbyuseridMaskGuide,
      data: {
        userId: app.userId
      },
      success(res) {
        console.log(res);
        if (res.data.code == 1) {
          let list = res.data.data;
          for (var i in list) {
            if (list[i].maskName == 'jsxq' && list[i].maskState == 0) {
              _this.setData({
                showMaskGuide: false,
                szmCMaskId: list[i].szmCMaskId
              })
            }
          }
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  // 配送说明 goExplainInfo
  goExplainInfo(e) {
    wx.navigateTo({
      url: '/cart/settlement/explain/explain',
    })
  },
  shopServiceInfo(e) {
    wx.navigateTo({
      url: '/cart/settlement/service/service',
    })
  },
})