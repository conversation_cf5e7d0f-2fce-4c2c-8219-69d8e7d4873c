import config from '../../../config.js';
var app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    datas:[],
    isSelected:[],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var _this = this;
    var storeId = wx.getStorageSync('shopInfo').szmCStore.storeId;
    app.request({
      url: config.shopServiceInfo,
      data: {
        storeId: storeId,
      },
      success(res) {
        console.log(res);
        if (res.data.code == 1) {
          var isSelected = false;
          var isSelectedArr = [];
          for(var i in res.data.data){
            isSelectedArr.push(isSelected);
          }
          _this.setData({
            datas: res.data.data,
            isSelected: isSelectedArr,
          })
          console.log(_this.data);
        } else {

        }
      }
    })
  },
  itemSelected: function (e) {
    var _this = this;
    var index = e.currentTarget.dataset.index;
    var list = _this.data.isSelected;
    var selected = list[index];
    list[index] = !selected;
    console.log(list);
    _this.setData({
      isSelected: list,
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },



})