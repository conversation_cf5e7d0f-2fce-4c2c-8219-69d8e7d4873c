import config from '../../config.js';
import util from '../../utils/util.js';
import cart from '../../utils/request/cart.js';
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isLoginUser: false,
    dikou: 0,
    distribution: 0,
    integralState: 0,
    total: '',
    imgUri: app.imgUri,
    groom: [],
    moreGroom: true, //推荐商品模板 是否显示更多按钮
    groomBackground: app.imgUri + '/images/index/yq-bg-3.png', //推荐商品模板
    groomTitle: '为您推荐',
    hideCount: true,
    number: 4,
    count: 0,
    needAni: false,
    hide_good_box: true,
    groomSmall: [],
    showCloseTip: false,
    storeServicetime: '',
    groom: [],
    groomSmall: [],
    groomleft: [],
    groomright: [],
  },
  //查看商品详情 
  goGoodsInfo: function (e) {
    var _this = this;
    var id = e.currentTarget.dataset.id,
      xs = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: '/pages/goods/goods?id=' + id + '&xs=' + xs,
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this,
      shopInfo = wx.getStorageSync('shopInfo');
    this.busPos = {};
    this.busPos['x'] = 25;
    this.busPos['y'] = 515;
    // 获取数组的长度 然后生成一个数组 用于循环每个商品的缩略图

    this.setData({
      dikou : options.dikou || 0,
      total : options.money || '',
      storeServicetime: shopInfo.szmCStore.storeServicetime,
      distribution: shopInfo.szmCStore.distribution,
      integralState: shopInfo.szmCStore.integralState,
    })
    
    if(shopInfo.szmCStore.integralState) {
      this.recommendCommoDitiesFun();
    }
    if (!util.judgeTimeInOpen(shopInfo.szmCStore.storeServicetime.split("-")[0], shopInfo.szmCStore.storeServicetime.split("-")[1])) {
      this.setData({
        showCloseTip: true
      })
    }

  },
  // 监听滚动条坐标
  onPageScroll: function (e) {
    var that = this
    var scrollTop = e.scrollTop
    var backTopValue = scrollTop > 500 ? true : false;
    var isShowCar = scrollTop > 150 ? true : false;
    that.setData({
      backTopValue: backTopValue,
      isShowCar: isShowCar,
    })
  },
  goTarget(e) {
    if (e.currentTarget.dataset.type == 1) {
      wx.switchTab({
        url: '/pages/indexNew/indexNew',
      })
    } else {
      wx.switchTab({
        url: '/pages/user/index/index',
      })
    }
  },
  moreMeal: function (e) {
    wx.switchTab({
      url: '/pages/classify/classify',
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let login = wx.getStorageSync('login');
    if (login == '' || login == '暂无用户数据' || login == null || login == undefined) {
      util.gotoLogin(1);
      return;
    }
    this.setData({
      isLoginUser: true,
    })
    var _this = this;
    app.request({
      url: config.getCartNum,
      data: {
        userId: app.userId,
        shopId: app.storeId + '',
      },
      method: "POST",
      success(res) {
        console.log(res);
        if (res.data.code == 1) {
          wx.setStorageSync('cartNum', res.data.data);
          _this.setData({
            count: res.data.data
          })
          wx.setTabBarBadge({
            index: 3,
            text: res.data.data + '',
          })
        } else {
          wx.setStorageSync('cartNum', 0);
          _this.setData({
            count: 0
          })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  // 推荐商品
  recommendCommoDitiesFun: function (e) {
    let _this = this;
    util.ajax(config.selectdikou, {
      'storeId': app.storeId,
      userId: app.userId,
    }, function (res) {
      wx.hideLoading();
      if (res.data.code == 1) {
        app.wxCache.put("groom", res.data.data, 10);
        // 获取数组的长度 然后生成一个数组 用于循环每个商品的缩略图
        var groom = res.data.data;
        var groomleft = [];
        var groomright = [];
        var groomSmall = [];
        for (var i = 0; i < groom.length; i++) {
          var hide_good_box = {
            'hide_good_box': true
          };
          groomSmall.push(hide_good_box);
          if (i % 2 == 0) {
            // 偶数放left
            groomleft.push(groom[i]);
          }
          if (i % 2 == 1) {
            // 偶数放left
            groomright.push(groom[i]);
          }
        }
        _this.setData({
          groom: res.data.data,
          groomSmall: groomSmall,
          groomleft: groomleft,
          groomright: groomright,
        })
      } else {
        _this.setData({
          groom: [],
          groomSmall: [],
          groomleft: [],
          groomright: [],
        })
      }
    })
  },
  touchOnGoods: function (e) {
    if (!this.data.isLoginUser) {
      util.gotoLogin()
      return
    }
    // 快速添加会产生异常
    if (this.data.addToCart) {
      return
    }
    // 如果good_box正在运动
    var id = e.currentTarget.dataset.index; //索引值
    var goodsId = e.currentTarget.dataset.id; // modelId 用于加入购物车
    var price = e.currentTarget.dataset.price; // modelId 用于加入购物车
    if (!this.data.groomSmall[id].hide_good_box) return;
    this.setData({
      addToCart: true
    })
    setTimeout(() => {
      this.setData({
        addToCart: false
      })
    }, 1500)
    this.startAnimation(id, goodsId, price);
  },
  startAnimation: function (id, goodsId, price) {
    if (!this.data.isLoginUser) {
      util.gotoLogin();
      return;
    }
    wx.request({
      url: config.addCart,
      data: {
        "productModelId": goodsId,
        "productNum": 1,
        "productPrice": price,
        "r1": "0",
        "userId": app.userId,
        "r3": app.storeId,

      },
      method: "POST",
      success(res) {
        if (res.data.code == 1) {
          wx.showToast({
            title: '加入购物车成功',
          })
          app.wxCache.put('cartNum', res.data.data, 10);
          wx.setTabBarBadge({
            index: 3,
            text: res.data.data + ''
          })
          that.setData({
            count: wx.getStorageSync('cartNum')
          })
        } else {
          wx.showToast({
            title: res.data.data,
            icon: 'none',
            duration: 1200,
          })
        }
      },
    })
  },
  //查看商品详情
  goGoodsInfo: function (e) {
    var _this = this;
    var id = e.currentTarget.dataset.id,
      xs = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: '/pages/goods/goods?id=' + id + '&xs=' + xs,
    })
  },
  // 加入购物车 成功
  sureAdd: function (e) {
    var _this = this;
    var id = e.currentTarget.dataset.id;
    var price = e.currentTarget.dataset.price;
    var dikou = e.currentTarget.dataset.dikou;
    var peisongfei = e.currentTarget.dataset.peisongfei;
      if (!this.data.isLoginUser) {
        util.gotoLogin();
        return;
      }
      wx.showLoading({
        title: '操作中',
        mask: true
      })
      wx.request({
        url: config.addCart,
        data: {
          "productModelId": id,
          "productNum": 1,
          "productPrice": price,
          "dikou": dikou,
          "peisongfei": peisongfei,
          "r1": 0,
          'r3': app.storeId,
          "userId": app.userId,
          'r4': "1"
        },
        method: "POST",
        success(res) {
          wx.hideLoading();
          if (res.data.code == 1) {
            wx.showLoading({
              title: '操作中',
              mask: true
            })
            cart.cartList({
              userId: app.userId,
              shopId: app.storeId + '',
            }, function (re) {
              wx.hideLoading();
              if (re.data.code == 1) {
                var datas = re.data.data;
                var datasArr = [];
                var s = '';
                for (var i in datas) {
                  if (id == datas[i].cartShop.skuId) {
                    datasArr.push(datas[i])
                    wx.setStorageSync('cartItems', datasArr);
                    wx.navigateTo({
                      url: '/cart/dikousettle/dikousettle?number=' + _this.data.goods_num,
                    })
                  }
                }
              }
            })
            _this.getCartNum();
            wx.setStorageSync('cartNum', res.data.data);
            _this.setData({
              count: wx.getStorageSync('cartNum')
            })
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.data.data,
              icon: 'none',
              duration: 1200,
            })
          }
        }
      })
  },
})