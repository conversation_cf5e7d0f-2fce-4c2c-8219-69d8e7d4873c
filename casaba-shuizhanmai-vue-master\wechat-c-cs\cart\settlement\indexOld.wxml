<!--cart/index/index.wxml  结算页面-->
<import src="../../pages/template/goods-item/meal/meal.wxml" />

<view class='page' wx:if="{{!(showMaskGuide&&isCanTarget)}}">
  <view class='con'>
    <!-- 收货信息 -->
    <view class='item1 df' style='width:100%;box-sizing: border-box' catchtap='chooseInfo'>
      <view class='left' style='width:88%;' wx:if="{{is_info}}">
        <view class='info'>
          <text class='name'>{{addressInfo.userName}}</text>
          <text class='phone'>{{addressInfo.telphoneOne}}</text>
          <text class='phone' style='margin-left:30rpx;'>{{addressInfo.r1 == 1 ? '企业':'个人'}}·{{addressInfo.r2 == 1 ? '电梯房':'非电梯房'}}·{{addressInfo.r3 == 8 ?'7楼以上': addressInfo.r3+'楼'}} {{addressInfo.r4 != null ?addressInfo.r4 :''}}</text>
        </view>
        <view class='address'>
          <text class='default' wx:if="{{addressInfo.isDefaultAddress == 0}}">默认</text>
          <text>{{addressInfo.province}}{{addressInfo.city}}{{addressInfo.area}}{{addressInfo.street}}</text>
        </view>
      </view>
      <view class='left' style='width:88%;' wx:else bindtap='chooseInfo'>
        <view class='info'>请先添加收货信息</view>
      </view>
      <view class='' style='flex:1;text-align:right;padding-right:20rpx;'>
        <image src='/images/shop/r-icon.png' mode="widthFix" style="width:22rpx"></image>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class='goods-item content meal'>
      <view wx:for="{{cartItems}}" class='df' wx:if="{{item.selected}}">
        <view data-id="{{item.cartId}}" class='cart-box df meal' style='transition: all 0.5s; {{list[index].Style}}' data-index="{{index}}">
          <!-- 普通商品 -->
          <view class='cart_main df' wx:if="{{item.cartShop != null && item.cartShop.skuNumber != '商品数量不足' && item.cartShop.skuState == 0}}">
            <view class='left df'>
              <view class='left_imgae df'>
                <image class="cart-image" src="{{item.cartShop.skuPicture}}"></image>
              </view>

            </view>
            <view class='right df' style='flex:1'>
              <view class='left_box '>
                <text class="cart-title">{{item.cartShop.skuName}}</text>
                <text class='cart-rule'>规格：{{item.cartShop.skuValue}}</text>
                <view class='cart-right df'>
                  <view class='df' style='align-items: flex-end;' wx:if='{{item.cartShop.vipPrice != null}}'>
                    <text class="cart-price"><text class='money'>￥</text>
                    <text>{{item.cartShop.vipPrice}}</text></text>
                    <text class="old-price"><text class='money'>￥</text>
                    <text>{{item.prouderPrice}}</text></text>
                  </view>
                  <view class='df' wx:else>
                    <text class="cart-price"><text class='money'>￥</text>
                    <text>{{item.prouderPrice}}</text></text>
                  </view>
                  <view class='df'>
                    <view class="input cart-text">x {{item.prouderNum}}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- 套餐 -->
          <view class='cart_main  item item-re' wx:if="{{item.cartShop == null }}">
            <view class='marker' style='display:block;' wx:if="{{item.cartShopGroupList.groupType}}">{{item.cartShopGroupList.groupType}}</view>
            <view class='df'>
              <view class='item-img'>
                <image src='{{item.cartShopGroupList.skuPicture}}'></image>
              </view>
              <view class='item-text'>
                <view class='title'>{{item.cartShopGroupList.groupName}}</view>
                <view class='info df'>
                  <view class='price df'>
                    <text class='new-price weight'><text class='money'>￥</text>{{item.cartShopGroupList.rulingPrice}}</text>
                    <text class='old-price '><text class='money'>￥</text>{{item.cartShopGroupList.originalPrice}}</text>
                  </view>
                  <view class='df'>
                    <view class="input cart-text">x {{item.prouderNum}}</view>
                  </view>
                </view>
              </view>
            </view>
            <view class='re-info df' wx:for="{{item.cartShopGroupList.list}}" wx:for-item="i">
              <view style='font-size:24rpx;color:#888;'>
                <view>{{i.skuName}}</view>
                <view>
                  <text>x</text>{{i.skuNumber}}</view>
              </view>
              <view class='re-price'>
                <text class='re-money'>￥</text>{{i.productDoller}}</view>
            </view>
          </view>
        </view>
        <view class='del' data-index="{{index}}" data-id="{{item.cartId}}" style='{{list[index].txtStyle}}' catchtap="slideDeleteProd">删除 </view>
      </view>

    </view>

    <!-- <view class='item2'>
      <view class="weui-cell weui-cell_access we-item" bindtap='shopServiceInfo' hover-class="weui-cell_active">
          <view class="weui-cell__bd we-title">商家服务</view>
          <view bindtap='goService' class="weui-cell__ft weui-cell__ft_in-access we-desc">免费服务</view>
      </view>
    </view> -->
    <view class='item2' wx:if="{{isCanTarget}}">
      <view class="weui-cell weui-cell_access we-item" catchtap='selectTicket' hover-class="weui-cell_active">
        <view class="weui-cell__bd we-title" style='color:#ff2f43;font-weight:bold;'>可用水票购买</view>
        <view bindtap='goWaterTicket' wx:if="{{useTicketNum == 0}}" class="weui-cell__ft weui-cell__ft_in-access we-desc 0" style='{{discountFlag?"color:#000;font-weight:bold;":""}}'>{{useTicketNumTitle}}</view>
        <view bindtap='goWaterTicket' wx:else style='color:#000;font-weight:bold;' class="weui-cell__ft weui-cell__ft_in-access we-desc 1">水票*{{useTicketNum}}</view>
      </view>
    </view>
    <!-- 配送方式 商家服务 优惠方式 等 -->
    <view class='item2'>
      <view class="weui-cell weui-cell_access we-item" bindtap='goExplainInfo' hover-class="weui-cell_active">
        <view class="weui-cell__bd we-title">配送方式</view>
        <!-- <view bindtap='goExplain'  class="weui-cell__ft weui-cell__ft_in-access we-desc">商家配送 配送说明</view> -->
        <view bindtap='goExplain' class="weui-cell__ft weui-cell__ft_in-access we-desc bold" style='color:#333;'>配送说明</view>
      </view>
    </view>
    <!-- 支付方式 -->
    <!-- <view class='item2'>
      <view class="weui-cell weui-cell_access we-item "  catchtap='selectSex' hover-class="weui-cell_active">
          <view class="weui-cell__bd we-title">支付方式</view>
          <view class="weui-cell__ft weui-cell__ft_in-access we-desc">{{payType}}</view>
      </view>
    </view> -->
    <view class='item2 b-t-10'>
      <!-- 优惠商品 -->

      <view class='tt df bold' wx:if="{{goodsTotalOriginal != '0.00'}}">
        <text>商品原价</text>
        <view>
          <text>+ </text>
          <text class='money'>￥</text>
          <text class='price'>{{goodsTotalOriginal}}</text>
        </view>
      </view>
      <view class='tt df color-red' wx:if="{{goodsTotal != '0.00' && goodsTotal != goodsTotalOriginal}}">
        <text>商品优惠价</text>
        <view>
          <text>+ </text>
          <text class='money'>￥</text>
          <text class='price'>{{goodsTotal}}</text>
        </view>
      </view>
      <view class='tt df color-red' wx:if="{{useTicketNum != 0 }}">
        <text>水票抵用金额</text>
        <view>
          <text>- </text>
          <text class='money'>￥</text>
          <text class='price'>{{discount}}</text>
        </view>
      </view>
      <view class='tt df color-red' wx:if="{{goodsTotalDiscounts != '0.00'}}">
        <text>商品立省</text>
        <view>
          <text>- </text>
          <text class='money'>￥</text>
          <text class='price'>{{goodsTotalDiscounts}}</text>
        </view>
      </view>
      <!-- <view class='tt df' wx:if="{{isOnlyGroup != 1 && discount != '0.00'}}">
          <text>水票抵用金额</text>
          <view>
            <text>- </text>
            <text class='money'>￥</text>
            <text class='price'>{{discount}}</text>
          </view>
        </view> -->

      <!-- 套餐价格信息 -->

      <view class='tt df bold' style='border-top:2rpx dashed #ccc;' wx:if="{{groomsTotalOriginal != '0.00'}}">
        <text>套餐原价</text>
        <view>
          <text>+ </text>
          <text class='money'>￥</text>
          <text class='price'>{{groomsTotalOriginal}}</text>
        </view>
      </view>
      <view class='tt df color-red' wx:if="{{groomsTotal != '0.00'}}">
        <text>套餐优惠价</text>
        <view>
          <text>+ </text>
          <text class='money'>￥</text>
          <text class='price'>{{groomsTotal}}</text>
        </view>
      </view>
      <view class='tt df color-red' wx:if="{{groomsTotalDiscounts != '0.00'}}">
        <text>套餐立省</text>
        <view>
          <text>- </text>
          <text class='money'>￥</text>
          <text class='price'>{{groomsTotalDiscounts}}</text>
        </view>
      </view>
      <!-- <view class='tt df'>
        <text>上楼费</text>
        <view>
          <text>+ </text>
          <text class='money'>￥</text>
          <text class='price'>{{upPrice?upPrice:'0'}}</text>
        </view>
      </view> -->
      <view class='tt df' style='border-top:2rpx dashed #ccc;' wx:if="{{freightAndUpfloor != '0.00'}}">
        <text>上楼费</text>
        <view class='df'>
          <text class='money'>+ </text>
          <text class='money'>￥</text>
          <text class='price'>{{freightAndUpfloor? freightAndUpfloor : "0.00"}}</text>
        </view>
      </view>
    </view>
    <!-- 留言 -->
    <view class='words' style="padding-bottom:100rpx;">
      <view class="font-size-30 bold color-blue">给商家,派单员留言</view>
      <view class=" weui-cells_after-title wx-words">
        <view class="weui-cell">
          <view class="weui-cell__bd">
            <textarea wx:if="{{is_blur}}" class="weui-textarea" placeholder='' style="height: 40px;" focus='{{onFocus}}' bindblur="onShowText" data-type="remark" bindinput='getRemarks' value='{{remarks}}'></textarea>
            <view class='text' style="height: 40px" bindtap='onShowTextarea' wx:else>{{remarks}}</view>
          </view>
        </view>
      </view>
      <view class="color-red margin-top-20 font-size-26" wx:if="{{isUserTicket == 1 && isShowYF}}">支持月付</view>
    </view>
    
  </view>
  <view class='footer'>
    <view class='cont df'>
      <view>
        <text>合计</text>
        <text class='money'>￥</text>
        <text class='price'>{{total}}</text>
      </view>
      <view class='button' bindtap='goSettlement'>去结算</view>
    </view>
  </view>
  <!-- 押金支付 view -->
  <view class='deposit_pay' wx:if="{{is_depositPay}}" style='height:{{windowHeight}}rpx'>
    <view class='deposit_pay_box' style=' text-align:center;'>
      <view class='title'>
        <view class='close' bindtap='closeDepositPay'>
          <image src='/images/pop-close-icon.png'></image>
        </view>
        <view class='text'>余额支付</view>
      </view>
      <view class="jine">金额</view>
      <view class='jine_num'>
        <text class='icon'>￥</text>
        <text class='num'>{{total}}</text>
      </view>
      <paySix bindvalueSix="valueSix" input_value="{{inputData.input_value}}" value_length="{{inputData.value_length}}" isNext="{{inputData.isNext}}" get_focus="{{true}}" focus_class="{{inputData.focus_class}}" value_num="{{inputData.value_num}}" height="{{inputData.height}}"
        width="{{inputData.width}}" see="{{inputData.see}}" interval="{{inputData.interval}}">
      </paySix>
      <view class='forget_pwd' bindtap='forgetPwd'>
        <text>忘记密码？</text>
      </view>
    </view>
  </view>
</view>
<!-- showModalStatus -->
<view class="commodity_screen" bindtap="hideModal" wx:if="{{showModalStatus}}"></view>
<view animation="{{animationData}}" class="commodity_attr_box" wx:if="{{showModalStatus == 1}}">
  <!-- 支付价格 -->
  <view class='pay-title'>
    <view class='bigMoney margin-bottom-20'>
      <text style='font-size:36rpx;'>￥</text>{{total}}</view>
    <!-- <view class='smaMoney' wx:if="{{discount != 0}}">原价：<text class='line'>￥{{oldTotal}}</text></view> -->
    <!-- <view class='smaMoney' wx:else></view> -->
  </view>




  <!-- 支付方式 -->
  <!-- 充值卡支付 -->
  <radio-group class="radio-group">
    <!-- 月付 -->
    <view class='pay-type ' wx:if="{{isUserTicket == 1 && isShowYF}}">
      <view class='left'>
        <image class='pay_card_img ' src='/images/zujinyuefu.png'></image>
        <view class='fl' style='margin-left:42rpx;font-size:36rpx;'>
          <view class=''>月付</view>
          <!-- <view class='shengyu'>剩余<text style='font-size:20rpx;'>￥</text> 300.00</view> -->
        </view>
      </view>
      <radio bindtap='payChange' class='fr' data-pay-type='月付' value='月付' checked="{{payType=='月付'}}"></radio>
    </view>
    <!-- 余额支付 -->
    <!-- <view class='pay-type' wx:if="{{isbalance == 1}}">
      <view class='left'>
        <image class='pay_card_img ' src='/images/yuezhifu.png'></image>
        <view class='fl' style='margin-left:42rpx;font-size:36rpx;'>
          <view class=''>余额支付</view>
          <view class='shengyu'>剩余
            <text style='font-size:20rpx;'>￥</text>{{balance}}</view>
        </view>
      </view>
      <radio bindtap='payChange' checked='checked' class='fr' data-pay-type='余额支付' value='余额支付'></radio>
    </view> -->
    <!-- 微信支付 -->
    <block wx:else>
      <view class='pay-type '>
        <view class='left'>
          <image class='pay_card_img ' src='/images/weixinzhifu.png'></image>
          <view class='fl' style='margin-left:42rpx;font-size:36rpx;'>
            <view class=''>微信支付</view>
            <!-- <view class='shengyu'>剩余<text style='font-size:20rpx;'>￥</text> 300.00</view> -->
          </view>
        </view>
        <radio bindtap='payChange' checked='{{isbalance == 0 ? "checked":""}}' class='fr' data-pay-type='微信支付' value='微信支付'></radio>
      </view>
      <!-- 货到付款 -->
      <view class='pay-type ' wx:if="{{isUserTicket == 1}}">
        <view class='left'>
          <image class='pay_card_img ' src='/images/huodaofukuan.png'></image>
          <view class='fl' style='margin-left:42rpx;font-size:36rpx;'>
            <view class=''>货到付款</view>
            <!-- <view class='shengyu'>剩余<text style='font-size:20rpx;'>￥</text>300.00</view> -->
          </view>
        </view>
        <radio bindtap='payChange' class='fr' data-pay-type='货到付款' value='货到付款'></radio>
      </view>
    </block>
    
    <!-- 月付 灰色-->
  <view class='pay-type ' wx:if="{{isUserTicket == 0  && isShowYF}}">
    <view class='left'>
      <image class='pay_card_img ' src='/images/zujinyuefu-gray.png'></image>
      <view class='fl' style='margin-left:42rpx;font-size:36rpx;'>
        <view class='' style='color:#aaaaaa;'>月付</view>
      </view>
    </view>
  </view>
    <!-- 余额支付 灰色 -->
    <!-- <view class='pay-type' wx:if="{{isbalance == 0}}">
      <view class='left'>
        <image class='pay_card_img ' src='/images/yuezhifu-gray.png'></image>
        <view class='fl' style='margin-left:42rpx;font-size:36rpx;'>
          <view class='' style='color:#aaaaaa;'>余额支付</view>
          <view class='shengyu' style='color:#aaaaaa;'>剩余
            <text style='font-size:20rpx;'>￥</text>{{balance}}</view>
        </view>
      </view>
    </view> -->
  </radio-group>
  <!-- 货到付款 灰色-->
  <view class='pay-type ' wx:if="{{isUserTicket == 0}}">
    <view class='left'>
      <image class='pay_card_img ' src='/images/huodaofukuan-gray.png'></image>
      <view class='fl' style='margin-left:42rpx;font-size:36rpx;'>
        <view class='' style='color:#aaaaaa;'>货到付款</view>
      </view>
    </view>
  </view>
  
  <!-- 确认支付按钮 -->
  <view class='confirm-modal'>
    <view bindtap='payMoney' class='confirm-btn pay-confirm-btn'>
      确认支付
    </view>
  </view>
  <view class='closeIcon' catchtap='hideModal'>
    <image src='/images/close.png'></image>
  </view>
</view>

<view class='mask-guaid-image' wx:if="{{showMaskGuide&&isCanTarget}}">
  <view class="left-top" wx:if="{{showGuideImage!=1}}" catchtap="showNextImageLast" style="position:absolute">跳过</view>
  <image wx:if="{{showGuideImage==1}}" catchtap="showNextImageLast" src="{{imgUri}}/images/maskGuide/keyongshuipiao-one.png?v={{random}}"></image>
</view>