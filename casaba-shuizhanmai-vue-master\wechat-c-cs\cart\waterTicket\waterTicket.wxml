<!-- 水票 -->
<view class='page'>
  <view class='cartMain' wx:if="{{cartItems.length>0}}">
    <view class='tips'>温馨提示: 请勾选您想要使用的水票,点击确定使用即可享受优惠！水票除了直接兑换相应的商品，还可以以您当初购买水票时的价格来抵扣水票专区内的商品！</view>
    <view wx:for="{{cartItems}}" class='' style='padding:20rpx 0;margin:20rpx;border-radius:15rpx; border-bottom:2rpx solid #ccc;box-shadow:0 0 15rpx rgba(100,100,100,.2);'>
      <view data-id="{{item.relevancesId}}" bindtap="selectedCart" class='cart-box df' style='transition: all 0.5s; {{list[index].Style}}' data-index="{{index}}">
        <view class='icon '>
          <view>
            <view wx:if="{{item.selected}}">
              <image src='{{imgUri}}/images/check-yes-blue.png'></image>
            </view>
            <view wx:else>
              <image src='/images/check-no.png'></image>
            </view>
          </view>
        </view>
        <view style='flex:1'>
          <view class="flex " style="padding:24rpx 24rpx 0 24rpx;justify-content: space-between;align-items: center;">
            <view class="font-size-24" style="color:#4ece1c" wx:if="{{item.isUsable==1}}">本次交易该水票可直接兑换商品</view>
            <view class="font-size-24 color-red" wx:else>该水票每张可抵扣{{item.price}}元</view>
            <view class="flex font-size-26" style="align-items: flex-end">
              <text>剩余可用</text>
              <image wx:for="{{item.split}}" class='img' style="width:32rpx;height:46rpx;margin:0 10rpx;" src='/images/number/{{item}}.png'></image>
              <text>张</text>
            </view>
          </view>

          <view class='cart_main df' style='background:url("{{item.background}}?r={{random}}") ;background-size:100% 100%;width:88%;'>
            <view class='left' wx:if="{{item.disprice}}" style="margin-top:-16rpx;">
              <view class='font-size-24 color-red'>
                <text>￥</text>
                <text class='font-size-42 bold color-red'>{{item.disprice}}</text>
                <view>元/张</view>
              </view>
              <view class='font-size-20' style="text-decoration: line-through">
                <text>￥</text>
                <text class=''>{{item.price}}</text> 元/张
              </view>
            </view>
            <view class='left' wx:else>
              <view class='font-size-24 margin-bottom-10'>
                <text>￥</text>
                <text class='font-size-42 bold '>{{item.price}}</text>
                <view>元/张</view>
              </view>
            </view>
            <view class='right'>
              <!-- <view>品牌：{{item.brand}}</view> -->
              <view class='rule bold font-size-28'>
                <text class='font-size-28 ' style='font-weight:400'>适用于：</text>{{item.specification}}</view>
              <view>订水电话：{{item.phone}}</view>
            </view>
          </view>

        </view>
      </view>
    </view>
    <view class='botton'>
      <view class='botton-box' bindtap='sureUseTicket'>确定使用</view>
    </view>

  </view>
  <view class='no' wx:else>
    <image src='/images/noTicket.png'></image>
    <view class='title'>暂无可使用的水票</view>
  </view>
</view>



<!-- 模态框 -->
<view class="modal-mask" bindtap="hideModal" catchtouchmove="preventTouchMove" hidden="{{showModal}}"></view>
<view class="modal-dialog font-size-30" hidden="{{showModal}}">
  <view class=' text-align-center bold position-relative' style='height:7%;z-index:100'>
    水票抵用
    <image src="/images/closeModal.png" style="width:50rpx;height:50rpx; right:-30rpx;" class='position-absolute' catchtap='hideModal'></image>
  </view>
  <scroll-view scroll-y class='width-full' style='height:82%;'>
    <swiper duration="500" bindchange="changeCurrent" style='height:100%;' current="{{currenId}}">
      <block wx:for="{{resultList}}">
        <swiper-item>
          <scroll-view scroll-y style="height:440rpx">
            <view>
              <text class='color-red font-size-30 bold'>抵用方案:</text>
            </view>
            <view class='margin-top-10 font-size-30' style='color:#000;line-height:58rpx;'>
              <view wx:for="{{item.list}}" wx:for-item="i">
                <text>{{index+1}}、</text> {{i.waterName}} * {{i.number}}张</view>
            </view>
          </scroll-view>
        </swiper-item>
        <view class='' style='border-top:2rpx dashed #ccc;line-height:60rpx;position: absolute;bottom:20rpx;width:100%;padding-top:20rpx;'>
          <view class='font-size-30  flex justify-content-between' style='colot:#666;'>
            <view>订单应付金额：</view>
            <view>
              <text class=''><text class='font-size-26'>￥</text>{{item.total}}元</text>
            </view>
          </view>
          <view class='font-size-30  flex justify-content-between' style='colot:#666;'>
            <view>水票抵用金额:</view>
            <view>
              <text class='color-red'><text>-</text>
              <text class='font-size-26'>￥</text>{{item.allDiscount}}元</text>
            </view>
          </view>
          <view class='font-size-30 blod flex justify-content-between'>
            <view>还需支付金额：</view>
            <view>
              <text class=''><text class='font-size-28'>￥</text>{{item.money}}元</text>
            </view>
          </view>
        </view>
      </block>
    </swiper>
  </scroll-view>
  <view class='app_btnArea margin-top-10' style='padding:0;'>

    <view class='flex justify-content-around'>
      <!-- <button form-type='submit' catchtap='changeItem' class='app_btn1 ' style='width:40%;'>换一个</button> -->
      <button style='width:40%;' catchtap='sureApply' class='app_btn'>确定使用</button>
    </view>
  </view>
</view>