import config from '../../config.js';
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imgUri: app.imgUri,
    cartItems: [],
    is_select: [],
    is_show: true,
    showModal: true, // 是否显示弹出层
    currenId: 0
  },
  // 方案切换
  changeCurrent(e) {
    console.log(e)
    this.setData({
      currenId: e.detail.current
    })
  },
  preventTouchMove(e) {},
  // 点击切换 
  changeItem(e) {
    var _this = this;
    var currenId = _this.data.currenId;
    if (currenId < 2) {
      currenId++
    } else {
      currenId = 0
    }
    _this.setData({
      currenId: currenId
    })
  },
  hideModal(e) {
    this.setData({
      showModal: true
    })
    console.log(123);
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log(options);
    var _this = this;
    _this.setData({
      total: options.total || 0
    })
    var cartItems = wx.getStorageSync('ticketList');
    if (cartItems) {
      _this.setData({
        cartItems: cartItems
      })
    } else {
      wx.request({
        url: config.isHaveTicket,
        data: {
          productModelId: JSON.parse(options.idArr),
          userId: app.userId,
          storeId: app.storeId,
        },
        method: "POST",
        success(res) {
          if (res.data.code == 1) {
            console.log(res);
            var cartItems = res.data.data;
            for (var i in cartItems) {
              cartItems[i].selected = false
            }
            _this.setData({
              cartItems: cartItems,
              is_show: true
            })
            console.log(cartItems)
            wx.setStorageSync('ticketList', cartItems)
          } else {
            wx.showToast({
              title: res.data.data,
              icon: 'none'
            })
            _this.setData({
              is_show: false,
              cartItems: ''
            })
          }
        },
      })
    }
  },

  // sureApply 确定使用
  sureApply(e) {
    var _this = this,
      currenId = _this.data.currenId,
      list = _this.data.resultList,
      useTicketArr = _this.data.useTicketArr,
      ticketList = _this.data.alldata;
    var sureUseTicket = list[currenId],
      allNumber = 0;
    var waterIdArr = [];
    console.log(_this.data.useTicketArr) // 直接兑换的水票总和
    console.log(sureUseTicket);


    for (var i in sureUseTicket.list) {
      allNumber += sureUseTicket.list[i].number
      sureUseTicket.list[i].allPrice = 0;
      for (var t in ticketList) {

        // arrB = arrB.concat(arrA.filter(a => !arrB.includes(a)))
        for (var j in useTicketArr) {
          var a = ticketList[t].count - ticketList[t].discountsNum; // 买的可使用的数量
          if (useTicketArr[j].couponId == sureUseTicket.list[i].waterId && sureUseTicket.list[i].waterId == ticketList[t].relevancesId) { // 水票id相等
            if (waterIdArr.indexOf(sureUseTicket.list[i].waterId) == -1) {
              waterIdArr.push(sureUseTicket.list[i].waterId);
            }
            if (a >= sureUseTicket.list[i].number) { // 可使用数量大于或等于使用的数量
              sureUseTicket.list[i].num = useTicketArr[j].num; //买的水票兑换的数量
              sureUseTicket.list[i].discountsNumWater = 0 //赠的水票兑换的数量
              sureUseTicket.list[i].deductNum = sureUseTicket.list[i].number - useTicketArr[j].num; //买的水票抵扣的数量
              sureUseTicket.list[i].discountsNum = 0 //赠的水票抵扣的数量
              break;
            } else { // 可使用数量小于使用的数量

              // 1、买的可使用的数量 大于等于 直接兑换商品的水票的使用数量 且小于等于 sureUseTicket.list[i].number 
              // sureUseTicket.list[i].number 实际使用水票的数量
              // useTicketArr[j].num 直接兑换商品的水票的使用数量（）
              var q = sureUseTicket.list[i].number - useTicketArr[j].num; // 抵扣的数量

              if (a >= useTicketArr[j].num) { // 买的数量 >= 兑换的数量

                sureUseTicket.list[i].num = useTicketArr[j].num; //买的水票兑换的数量
                sureUseTicket.list[i].discountsNumWater = 0; //赠的水票兑换的数量
                sureUseTicket.list[i].discountsNum = q - (a - useTicketArr[j].num) //赠的水票抵扣的数量
                sureUseTicket.list[i].deductNum = a - useTicketArr[j].num; //买的水票抵扣的数量
                break;
              } else { // 买的数量 < 兑换的数量

                sureUseTicket.list[i].num = a; //买的水票兑换的数量
                sureUseTicket.list[i].discountsNumWater = useTicketArr[j].num - a; //赠的水票兑换的数量
                sureUseTicket.list[i].deductNum = 0; //买的水票抵扣的数量
                sureUseTicket.list[i].discountsNum = q; //赠的水票抵扣的数量
                break;
              }
            }
            break;
          }
        }

      }

    }
    for (var t in ticketList) {
      var a = ticketList[t].count - ticketList[t].discountsNum; // 买的可使用的数量
      if (waterIdArr.length > 0) {
        for (var w in waterIdArr) {
          if (waterIdArr[w] != sureUseTicket.list[i].waterId) {
            if (a >= sureUseTicket.list[i].number) {
              sureUseTicket.list[i].deductNum = sureUseTicket.list[i].number; //买的水票抵扣的数量
              sureUseTicket.list[i].discountsNum = 0 //赠的水票抵扣的数量
            } else {
              sureUseTicket.list[i].deductNum = a; //买的水票抵扣的数量
              sureUseTicket.list[i].discountsNum = sureUseTicket.list[i].number - a; //赠的水票抵扣的数量
            }
            sureUseTicket.list[i].num = 0; //买的水票兑换的数量
            sureUseTicket.list[i].discountsNumWater = 0; //赠的水票兑换的数量
          }
        }
      } else {
        if (a >= sureUseTicket.list[i].number) {
          sureUseTicket.list[i].deductNum = sureUseTicket.list[i].number; //买的水票抵扣的数量
          sureUseTicket.list[i].discountsNum = 0 //赠的水票抵扣的数量
        } else {
          sureUseTicket.list[i].deductNum = a; //买的水票抵扣的数量
          sureUseTicket.list[i].discountsNum = sureUseTicket.list[i].number - a; //赠的水票抵扣的数量
        }
        sureUseTicket.list[i].num = 0; //买的水票兑换的数量
        sureUseTicket.list[i].discountsNumWater = 0; //赠的水票兑换的数量
      }
    }
    // for (var i in sureUseTicket){
    //   allNumber += sureUseTicket.list[i].number
    // }
    sureUseTicket.allNumber = allNumber;
    // console.log(_this.data.useTicketArr) // 直接兑换的水票总和
    console.log(sureUseTicket);
    // return;
    wx.setStorageSync('sureUseTicket', sureUseTicket); // 结算时用的水票数据
    wx.navigateBack({
      detal: 1
    })
  },

  // 查看使用方案 sureUseTicket
  sureUseTicket(e) {
    var _this = this;
    var cartItems = _this.data.cartItems;
    var list = wx.getStorageSync('cartItems'); // 购物车商品集合  
    var undata = []; //不能直接兑换的水票集合
    var data = []; //能直接兑换的水票集合
    var alldata = []; //选择的水票集合
    var total = _this.data.total; // 订单总金额
    // console.log(total, "总数");
    var allPrice = 0; // 使用水票兑换后剩余金额
    var discount = 0; // 使用水票兑换的金额
    var useTicketNum = 0; // 使用水票兑换的数量
    var len = cartItems.length,
      n = 0; // 判断有没有选择水票
    var isUseOther = 0; // 是否使用其他水票抵扣
    var is_click = 1;
    var useTicketArr = []; // 能直接兑换商品的水票的使用数量
    var isSelectOne = [];
    /*、
     ** 默认组合方式 1 0 0 0
     ** commonState    普通 0关闭 1开启
     ** timeState      限时 0关闭 1开启
     ** discountState  优惠 0关闭 1开启
     ** mealState      套餐 0关闭 1开启
     */
    var commonState = 1,
      timeState = 0,
      discountState = 0,
      mealState = 0;
    var allowUseWaterPrice = 0; //允许使用水票的商品零售总和


    // 对不同状态的水票集合赋值
    for (var i in cartItems) {
      if (cartItems[i].selected) {
        if (cartItems[i].isUsable == 0) {
          undata.push(cartItems[i]);
        } else {
          data.push(cartItems[i]);
        }
        alldata.push(cartItems[i]);
      } else {
        n++
      }
    }
    _this.setData({
      alldata: alldata
    })
    // 计算水票直接兑换商品的数量和金额
    if (commonState == 1 && timeState == 0 && discountState == 0 && mealState == 0) { // 纯普通商品
      for (var i in list) {
        if (list[i].cartShop != null && list[i].selected == true && (list[i].cartShop.newSource == 0 || list[i].cartShop.newSource == 3)) {
          var num = list[i].prouderNum;
          if (list[i].cartShop.newSource == 3){
            allowUseWaterPrice += list[i].prouderNum * list[i].cartShop.vipPrice
          }else{
            allowUseWaterPrice += list[i].prouderNum * list[i].prouderPrice
          }
          
          console.log(allowUseWaterPrice, '2222');
          console.log(list[i], '1111');
          if (data.length > 0) {  //能直接兑换的水票集合
            for (var j in data) {
              if (list[i].cartShop.skuId == data[j].productModelId) {
                if (isSelectOne.length > 0) {
                  var a = true
                  for (var n in isSelectOne) {
                    if (isSelectOne[n].goodsId == list[i].cartShop.skuId && isSelectOne[n].waterId == data[j].relevancesId) {
                      a = false;
                      break;
                    }
                  }
                  if (a) {
                    if (num <= data[j].count) { // 如果结算的商品数量 小于等于 水票的数量
                      if (list[i].cartShop.vipPrice != null && list[i].cartShop.vipPrice != ''){
                        discount += num * list[i].cartShop.vipPrice;
                      }else{
                        discount += num * list[i].prouderPrice;
                      }
                      useTicketArr.push({
                        couponId: data[j].relevancesId,
                        num: num,
                        itemPrice: Number(Number(num) * Number(list[i].prouderPrice)).toFixed(2),
                      })
                      useTicketNum += num;
                      isUseOther = 0;
                      isSelectOne.push({
                        'goodsId': list[i].cartShop.skuId,
                        'waterId': data[j].relevancesId
                      })
                    } else { // 如果结算的商品数量 大于 水票的数量 
                      if (list[i].cartShop.vipPrice != null && list[i].cartShop.vipPrice != '') {
                        discount += num * list[i].cartShop.vipPrice;
                      } else {
                        discount += num * list[i].prouderPrice;
                      }
                      useTicketArr.push({
                        couponId: data[j].relevancesId,
                        num: data[j].count,
                        itemPrice: Number(Number(data[j].count) * Number(list[i].prouderPrice)).toFixed(2),
                      })
                      num = Number(num) - Number(data[j].count)
                      useTicketNum += data[j].count;
                      isSelectOne.push({
                        'goodsId': list[i].cartShop.skuId,
                        'waterId': data[j].relevancesId
                      })
                    }
                  }
                } else {
                  if (num <= data[j].count) { // 如果结算的商品数量 小于等于 水票的数量
                    // discount += num * list[i].prouderPrice;
                    if (list[i].cartShop.vipPrice != null && list[i].cartShop.vipPrice != '') {
                      discount += num * list[i].cartShop.vipPrice;
                    } else {
                      discount += num * list[i].prouderPrice;
                    }
                    useTicketArr.push({
                      couponId: data[j].relevancesId,
                      num: num,
                      itemPrice: Number(Number(num) * Number(list[i].prouderPrice)).toFixed(2),
                    })
                    useTicketNum += list[i].prouderNum;
                    isUseOther = 0;
                    isSelectOne.push({
                      'goodsId': list[i].cartShop.skuId,
                      'waterId': data[j].relevancesId
                    })
                  } else { // 如果结算的商品数量 大于 水票的数量 
                    // discount += data[j].count * list[i].prouderPrice;
                    if (list[i].cartShop.vipPrice != null && list[i].cartShop.vipPrice != '') {
                      discount += num * list[i].cartShop.vipPrice;
                    } else {
                      discount += num * list[i].prouderPrice;
                    }
                    useTicketArr.push({
                      couponId: data[j].relevancesId,
                      num: data[j].count,
                      itemPrice: Number(Number(data[j].count) * Number(list[i].prouderPrice)).toFixed(2),
                    })
                    isSelectOne.push({
                      'goodsId': list[i].cartShop.skuId,
                      'waterId': data[j].relevancesId
                    })
                    num = Number(num) - Number(data[j].count)
                    useTicketNum += data[j].count;
                    isUseOther = 1;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 判断没有任何选择 提示
    if (len > 0 && len == n) {
      wx.showModal({
        title: '温馨提示',
        content: '您有可使用的水票，是否放弃优惠？',
        success(res) {
          if (res.confirm) {
            wx.navigateBack({
              detal: 1
            })
          }
        }
      })
    } else { // 判断结算的商品数量大于可使用的水票数量
      allPrice = Number(Number(allowUseWaterPrice) - Number(discount)).toFixed(2);
      if (is_click == 0) {
        return;
      }
      is_click = 0;
      var list = [];
      for (var i in alldata) {
        var item, count;
        if (useTicketArr.length > 0) {
          for (var j in useTicketArr) {
            if (useTicketArr[j].couponId == alldata[i].relevancesId) {
              count = alldata[i].count - useTicketArr[j].num;
              break;
            } else {
              count = alldata[i].count
            }
          }
          let price = 0;
          if (alldata[i].disprice != null && alldata[i].disprice != ''){
            price = alldata[i].disprice
          }else{
            price = alldata[i].price
          }
          item = {
            "number": count,
            "waterId": alldata[i].relevancesId,
            "waterMoney": price,
            "waterMoneyFace": alldata[i].mean,
            "waterName": alldata[i].brand + alldata[i].specification
          }
          list.push(item);
        } else {
          let price = 0;
          if (alldata[i].disprice != null && alldata[i].disprice != '') {
            price = alldata[i].disprice
          } else {
            price = alldata[i].price
          }
          item = {
            "number": alldata[i].count,
            "waterId": alldata[i].relevancesId,
            "waterMoney": price,
            "waterMoneyFace": alldata[i].mean,
            "waterName": alldata[i].brand + alldata[i].specification
          }
          list.push(item);
        }
      }


      var o = {
        list: list,
        orderMoney: allPrice,
        type: 0
      }
      console.log(o);
      // return;
      var url = config.useTicketAll;
      wx.request({
        url: url,
        data: o,
        method: "POST",
        success(res) {
          if (res.data.code == 1) {
            is_click = 1;
            var relist = [];
            var list = res.data.data[0]; // relist 返回的数据
            relist.push(list);
            var isHaveUse = [];
            // useTicketArr  可以直接进行兑换的数组
            for (var i in relist) { // 对返回的数据进行处理
              relist[i].total = total; // 将订单总价放入源数据
              relist[i].allDiscount = Number(allowUseWaterPrice - relist[i].money).toFixed(2); // 计算总优惠金额
              relist[i].money = Number(relist[i].total - relist[i].allDiscount).toFixed(2); // 剩余应付总金额
              for (var p in relist[i].list) { // 对返回的方案进行处理
                var allNumber = 0; // 总水票数量
                if (useTicketArr.length > 0) { // 可以直接进行兑换的数组大于零的时候
                  relist: for (var j in useTicketArr) {
                    if (isHaveUse.length > 0) {
                      var a = true;
                      for (var q in isHaveUse) {
                        if (isHaveUse[q] == useTicketArr[j].couponId) {
                          a = false;
                          break;
                        }
                      }
                      if (a) {
                        if (relist[i].list[p].state == 1) {
                          if (useTicketArr[j].couponId == relist[i].list[p].waterId) { //加上直接兑换的水票的数量
                            relist[i].list[p].number = relist[i].list[p].number + useTicketArr[j].num
                            isHaveUse.push(useTicketArr[j].couponId)
                            break relist;
                          } else {
                            relist[i].list[p].number = relist[i].list[p].number
                          }
                          allNumber += relist[i].list[p].number;
                        } else {
                          if (useTicketArr[j].couponId == relist[i].list[p].waterId) { //加上直接兑换的水票的数量
                            relist[i].list[p].number = useTicketArr[j].num
                            isHaveUse.push(useTicketArr[j].couponId)
                            break relist;
                          } else {
                            relist[i].list[p].number = 0;
                          }
                          allNumber += useTicketArr[j].num;
                        }
                      }
                    } else {
                      if (relist[i].list[p].state == 1) {
                        if (useTicketArr[j].couponId == relist[i].list[p].waterId) { //加上直接兑换的水票的数量
                          relist[i].list[p].number = relist[i].list[p].number + useTicketArr[j].num
                          isHaveUse.push(useTicketArr[j].couponId)
                          break relist;
                        } else {
                          relist[i].list[p].number = relist[i].list[p].number
                        }
                        allNumber += relist[i].list[p].number;
                      } else {
                        if (useTicketArr[j].couponId == relist[i].list[p].waterId) { //加上直接兑换的水票的数量
                          relist[i].list[p].number = useTicketArr[j].num
                          isHaveUse.push(useTicketArr[j].couponId)
                          break relist;
                        } else {
                          relist[i].list[p].number = 0;
                        }

                        allNumber += useTicketArr[j].num;
                      }
                    }
                  }
                }
                else {
                  if (relist[i].list[p].state == 1) {
                    relist[i].list[p].number = relist[i].list[p].number
                    allNumber += relist[i].list[p].number;
                  } else {
                    relist[i].list[p].number = 0;
                    allNumber += relist[i].list[p].number;
                  }
                }
                relist[i].allNumber = allNumber
              }

            }


            _this.setData({
              showModal: false,
              resultList: relist,
              useTicketArr: useTicketArr
            })
            console.log(_this.data.resultList, 'w sda a')
            console.log(relist, '方案', "useTicketArr", useTicketArr)
          }
        }
      })
    }
  },
  // 选择
  selectedCart: function(e) {
    var cartItems = this.data.cartItems; //获取购物车列表
    var index = e.currentTarget.dataset.index; //获取当前点击事件的下标索引
    var selected = cartItems[index].selected; //获取购物车里面的value值
    var _this = this;
    //取反
    cartItems[index].selected = !selected;
    _this.setData({
      cartItems: cartItems
    })
    wx.setStorageSync('ticketList', cartItems)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    var _this = this;
    _this.setData({
      random: Math.ceil(Math.random() * 100000)
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },


})