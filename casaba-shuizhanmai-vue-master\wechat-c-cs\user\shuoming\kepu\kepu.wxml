<view class="kepu-container">
  <!-- Banner Section -->
  <view class="banner-section">
    <swiper class='banner-swiper' indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" bindchange="swiperChange" circular indicator-active-color="#fff">
      <block wx:for="{{imgUrls2}}" wx:key="key" wx:for-index="index">
        <swiper-item>
          <image data-uri="{{item.r1}}" data-id='{{item.r2}}' data-type='{{item.type}}' catchtap='gotoActivityPage' src="{{item.path}}" class="banner-image" />
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- Content Section -->
  <view class="content-section">
    <!-- Section Header -->
    <view class="section-header">
      <view class="section-title">
        <text class="title-icon">📚</text>
        <text class="title-text">科普知识</text>
      </view>
      <view class="section-subtitle">探索科学世界，增长知识见闻</view>
    </view>

    <!-- Article List -->
    <view class="article-list">
      <view class="article-item" wx:for="{{kepu}}" wx:key="index" bindtap="godetail" data-id="{{item.id}}">
        <view class="article-image-container">
          <image class="article-image" src="{{item.image}}" mode="aspectFill" />
          <view class="image-overlay"></view>
        </view>
        <view class="article-content">
          <view class="article-title">{{item.name}}</view>
          <view class="article-meta">
            <view class="meta-item">
              <text class="meta-icon">👁</text>
              <text class="meta-text">{{item.view || 0}} 次浏览</text>
            </view>
            <view class="read-more">
              <text>阅读全文</text>
              <text class="arrow">→</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Empty State -->
    <view class="empty-state" wx:if="{{kepu.length === 0}}">
      <image class="empty-icon" src="/images/noGoods.png" mode="aspectFit" />
      <text class="empty-text">暂无科普内容</text>
      <text class="empty-subtitle">更多精彩内容即将上线</text>
    </view>
  </view>
</view>