<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-circle">
  <canvas class="van-circle__canvas" type="{{ type }}" style="width: {{ utils.addUnit(size) }};height:{{ utils.addUnit(size) }}" id="van-circle" canvas-id="van-circle"></canvas>
  <view wx:if="{{ !text }}" class="van-circle__text">
    <slot></slot>
  </view>
  <cover-view wx:else class="van-circle__text">{{ text }}</cover-view>
</view>
