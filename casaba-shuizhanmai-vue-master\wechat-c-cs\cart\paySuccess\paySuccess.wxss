@import '../../pages/template/goods-item/groom/groom.wxss';
.groom{height: auto;padding-bottom: 140rpx;}
.groom .item{width: 333rpx;height:433rpx;overflow: hidden;margin: 10rpx;}
.groom .item:nth-child(2n){margin-right: 0rpx;}
.groom .item:nth-child(2n-1){margin-left: 0rpx;}
.groom .item .marker{display: none;}
.groom .item .item-img image{width: 320rpx;height: 275rpx;}
.groom .item .item-text .info{justify-content: space-between;padding-right: 30rpx;}

.pay-title{padding: 30rpx;border-radius: 0rpx;}
.pay-title .cont{background: #fff;border-radius: 15rpx;text-align: center;color: #1dcafd;font-size: 30rpx;padding: 0 30rpx;}
.pay-title .cont .people{width: 183rpx;height: 303rpx;margin: 45rpx 0;}
.button{margin: 0 auto;margin-top: 10rpx; font-size: 24rpx; width: 220rpx;height: 60rpx;background: linear-gradient(to right,#1794fd,#68ddff);border-radius: 15rpx;color: #fff;line-height: 60rpx;}

/* pages/find/index/index.wxss */

.m-menu {
  background: #fff;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-bottom: 0rpx;
  padding-top: 25rpx;
}

.m-menu .item {
  width: 150rpx;
  height: 150rpx;
}

.m-menu image {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto;
  margin-bottom: 12rpx;
}

.m-menu text {
  display: block;
  font-size: 28rpx;
  text-align: center;
  margin: 0 auto;
  line-height: 1;
  color: #333;
}
.search {
  height: 88rpx;
  width: 100%;
  padding: 0 30rpx;
  background: #1971F2;
  display: flex;
  align-items: center;
}

.search .van-icon-search {
  line-height: 59rpx;
}

.search .input {
  width: 100%;
  height: 56rpx;
  background: #ededed;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search .txt {
  height: 42rpx;
  line-height: 42rpx;
  color: #666;
  padding-left: 10rpx;
  font-size: 26rpx;
}
 .h {
  width: 100%;
  display: flex;
  background: #fff;
  height: 84rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

 .h .item {
  display: inline-block;
  height: 82rpx;
  width: 50%;
  padding: 0 15rpx;
  text-align: center;
}

 .h .item .txt {
  display: inline-block;
  height: 82rpx;
  padding: 0 20rpx;
  line-height: 82rpx;
  color: #333;
  font-size: 30rpx;
  width: 170rpx;
}

 .h .item.active .txt {
  color: #1794fd;
  border-bottom: 4rpx solid #1794fd;
}

.meal {
	height: auto;
}

.searchbar-result {
	margin-top: 0;
	font-size: 14px;
}

.searchbar-result:before {
	display: none;
}

.weui-cell {
	padding: 12px 15px 12px 35px;
}

.w20 {
  width: 20%;
  margin-top: 10rpx;
}

.page__bd {
	margin-top: 30rpx;
}

.banner {
	/* width: 100vw; */
  height: 222rpx;
  margin: 0 30rpx;
}

.top-bg {
  width: 750rpx;
height: 674rpx;
background: linear-gradient(180deg, #1971F2 0%, rgba(55,116,246,0) 100%);
position: absolute;
	top: 0;
}

.banner .slide-image {
	width: 100%;
	margin: 0;
	height: 100%;
}

.slide-image {
	height: 388rpx;
}

.adtag {
	color: #ff2f2f;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 60rpx;
	height: 100%;
	box-sizing: content-box;
	background: #ffebed;
	padding: 4rpx 10rpx;
	margin-right: 10rpx;
	border-radius: 10rpx;
	flex-shrink: 0;
}

.guanggao {
	font-size: 24rpx;
	background: #FFFFFF;
	height: 90rpx;
}

.toutiao {
	background: rgba(255, 47, 67, 0.1);
	border-radius: 45rpx;
	padding: 0 10rpx;
	border: 2px solid #FF2F43;
	box-sizing: border-box;
}

.line-toutiao {
	width: 3rpx;
	height: calc(100% - 30rpx);
	background: #FF2F43;
}

.tongzhi {
	width: 37.5rpx;
	height: 40rpx;
}

.tongzhi1 {
	width: 37.5rpx;
	height: 40rpx;
	position: relative;
	top: -4rpx;
	margin-left: 4rpx;
	animation: shake 0.5s linear infinite;
}

@keyframes shake {
	0% {
		transform: translateX(-10rpx) scale(0.5);
	}

	50% {
		transform: translateX(0rpx) scale(1);
	}

	100% {
		transform: translateX(-10rpx) scale(0.5);
	}
}

/* Swiper 样式 */

.item-list {
	font-size: 30rpx;
	/* justify-content: space-between; */
	text-align: center;
	/* padding: 20rpx; */
	/* background: #fff; */
	flex-wrap: wrap;
  margin: 0 30rpx;
  border-radius: 16rpx;

}

.item-list .img {
	/* border-radius: 50%; */
	width: 90rpx;
	height: 90rpx;
}

.item-list .img-box {
	min-height: 110rpx;
}

.item-list .text {
	color: rgba(0, 0, 0, 0.9);
	font-size: 26rpx;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	word-break: break-all;
}

/* 限时购 限量购 */

.slide {
	height: 240rpx;
	width: 90%;
	position: relative;
	margin: 0 auto;
	margin-top: 100rpx;
}

.slide li {
	position: absolute;
	display: inline-block;
	width: 100%;
	overflow: hidden;
	box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.6);
	border-radius: 10rpx;
}

.time-buy .item {
	padding: 20rpx;
	height: 170rpx;
	position: relative;
	background: #fff;
	font-size: 28rpx;
}

.time-buy .item .marker {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	border-top-left-radius: 10rpx;
}
 .dowm {
  /* position: absolute;
  right: 0rpx;
  bottom: 0rpx; */
  width: 90%;
  margin-left: 5%;
  margin-top: 10rpx;
  height: 44rpx;
  background: #1971F2;
  border-radius: 8rpx;
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
  line-height: 44rpx;
  text-align: center;
}

.time-buy .item .marker image {
	width: 48rpx;
	height: 48rpx;
	border-top-left-radius: 10rpx;
}

.time-buy .item .item-img image {
	width: 159rpx;
	height: 159rpx;
}

.time-buy .item .item-text {
	width: 72%;
}

.time-buy .item .item-text .title {
	font-size: 28rpx;
	height: 22px;
	color: #000;
	display: block;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.time-buy .introduce,
.time-buy .introduce .schedule {
	color: rgba(0, 0, 0, 0.9);
	font-size: 24rpx;
}

.time-buy .item .item-text .info {
	justify-content: space-between;
}

.time-buy .item .item-text .info .price {
	align-items: flex-end;
}

.time-buy .item .item-text .info .old-price {
	font-size: 24rpx;
	color: rgba(0, 0, 0, 0.6);
	margin-left: 10rpx;
	text-decoration: line-through;
}
.old-pricebbb {
	font-size: 24rpx;
	color: #6f6f6f;
	margin-left: 10rpx;
}
.dikou {
  margin-top: 10rpx;
  font-size: 24rpx;
  height: 42rpx;
  line-height: 42rpx;
  background: #ffc3ca;
  border-radius: 21rpx;
  padding: 0 20rpx;font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 22rpx;
  color: #EE0A24;
  text-align: center;

}
.dikou123 {
  margin-top: 10rpx;
  font-size: 24rpx;
  height: 42rpx;
  line-height: 42rpx;
  border: 1rpx solid green;
  border-radius: 21rpx;
  padding: 0 20rpx;font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 22rpx;
  color: green;
  text-align: center;

}
.dikou123123 {
  margin-top: 10rpx;
  font-size: 24rpx;
  height: 42rpx;
  line-height: 42rpx;
  border: 1rpx solid #E46B00;
  border-radius: 21rpx;
  padding: 0 20rpx;font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 22rpx;
  color: #E46B00;
  text-align: center;

}
.dikou1234 {
  margin-top: 10rpx;
  font-size: 24rpx;
  height: 42rpx;
  line-height: 42rpx;
  border: 1rpx solid #0072ff;
  border-radius: 21rpx;
  padding: 0 20rpx;font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 22rpx;
  color: #0072ff;
  text-align: center;

}
.realprice {
  margin-top: 10rpx;
  font-size: 24rpx;
  height: 42rpx;
  line-height: 42rpx;
  border-radius: 21rpx;
  padding: 0 20rpx;font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 22rpx;
  color: #EE0A24;

}

.time-buy .item .item-text .info .new-price {
	font-size: 32rpx;
	color: #ff2f2f;
}

.time-buy .item .item-text .info .button {
	width: 130rpx;
	height: 40rpx;
	text-align: center;
	color: #fff;
	font-size: 24rpx;
	background: linear-gradient(to right, #1794fd, #68ddff);
	border-radius: 50rpx;
}

.time-buy .schedule text {
	color: #1794fd;
}

.time-buy .bar {
	height: 20rpx;
	background: #e1eeff;
	border-radius: 20rpx;
	margin: 10rpx 0;
	color: #0072ff;
	position: relative;
}

.time-buy .bar .per {
	position: absolute;
	right: 10rpx;
	font-size: 14rpx;
	top: 0;
}

.time-buy .bar-num .per {
	position: absolute;
	left: 10rpx;
	font-size: 14rpx;
	top: 0;
	color: #fff;
}

.time-buy .bar-num {
	height: 20rpx;
	background: linear-gradient(to right, #1794fd, #68ddff);
	border-radius: 20rpx;
}

/* footer */

.footer {
	font-size: 30rpx;
	color: #d2d2d2;
	justify-content: center;
}

.footer .line {
	width: 30rpx;
	height: 2rpx;
	background: #d2d2d2;
	margin: 0 10px;
}

.banner-swiper {
	width: 100%;
	height: 500rpx;
	overflow: hidden;
}

swiper {
	display: block;
	height: 500rpx;
	position: relative;
}

.slide-image {
	width: 96%;
	display: block;
	margin: 0 auto;
	height: 450rpx;
	margin-top: 25rpx;
}

.active {
	margin-top: 0rpx;
	height: 500rpx;
}

.meal {
	padding-bottom: 70rpx;
}

.meal .more-meal {
	color: #6c6c6c;
	font-size: 28rpx;
	position: absolute;
	right: 20rpx;
	top: 20rpx;
}

/* cz */

/* .groupBox{
  padding: 20rpx 30rpx;
  background: white;
} */

.waterBoxScroll {
	width: 100%;
	height: 350rpx;
	overflow: hidden;
}

.waterArticle {
	width: 291rpx !important;
	height: 312rpx;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	box-sizing: border-box;
}

.waterTicketBtn {
	width: 80rpx;
	height: 45rpx;
	line-height: 45rpx;
	text-align: center;
	background-color: #1693fd;
	color: white;
	border-radius: 10rpx;
}

.againBuy {
	font-size: 26rpx;
	border: 2rpx solid #aaa;
	color: #000;
	font-weight: bold;
	border-radius: 50rpx;
	text-align: center;
}

/* start by yanxingwang */

.home-top-index {
	width: 100%;
	font-size: 24rpx;
	z-index: 1001;
}

.index_search {
	margin: 0 30rpx;
}

.index-tip {
	background: #f03043;
	padding: 6rpx 16rpx;
	height: 56rpx;
	box-sizing: border-box;
	width: 100%;
}

.back-select-shop {
	padding-bottom: 20rpx;
	background-color: #fff;
}
.back-shop-btn{
	width: 400rpx;
	height: 60rpx;
	background: #6A9DEB;
	background: -moz-linear-gradient(left, #8CB5F3, #6A9DEB);
	background: -webkit-linear-gradient(left, #8CB5F3, #6A9DEB);
	background: -ms-linear-gradient(left bottom, #8CB5F3, #6A9DEB);
	background: linear-gradient(left, #8CB5F3, #6A9DEB);
	-ms-filter: "progid:DXImageTransform.Microsoft.gradient (GradientType=0, startColorstr=#8CB5F3, endColorstr=#6A9DEB)";
	border-radius: 0 60rpx 60rpx 0;
}
.back-shop-text{
	height: 60rpx;
	margin-right: 20rpx;
	line-height: 60rpx;
	color: #fff;
}
.back-shop-icon{
	width: 16rpx;
	height: 25rpx;
}
.content_shop_list {
	padding: 0 30rpx;
	/* background: linear-gradient(to bottom, #fff, #f6f6f6); */
}

.border_bottom_twenty {
	width: 100%;
	height: 20rpx;
	background: #f6f6f6;
}

.border-left-blue {
	line-height: 38rpx;
	padding-left: 10rpx;
	border-left: 6rpx solid #1794fd;
}

.groom .item-box-index {
	flex-wrap: wrap;
	margin-top: 25rpx;
	padding: 3rpx;
}

.groom .item-index {
	padding: 20rpx 2rpx;
	flex-wrap: wrap;
	/* height: 296rpx; */
	width: 220rpx;
	position: relative;
	background: #fff;
	border-radius: 10rpx;
	font-size: 26rpx;
	margin: 5rpx;
}

.groom .title {
	text-align: center;
	font-size: 34rpx;
}

.groom .item-index .marker {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 100rpx;
	height: 37rpx;
	background: #fa1e34;
	border-radius: 0 0 8rpx 8rpx;
	color: #fff;
	font-size: 22rpx;
	font-weight: bold;
	line-height: 37rpx;
	padding: 0 8rpx;
  z-index: 999;
}

.groom .item-index .vip {
	position: absolute;
	top: 0rpx;
	right: 0rpx;
	width: 62rpx;
	height: 63rpx;
}

.groom .item-index .item-img image {
	width: 212rpx;
	height: 159rpx;
}

.groom .item-index .item-text .title {
	font-size: 26rpx;
	height: 40px;
	text-align: left;
	padding: 0 10rpx;
	/* width: 200rpx; */
	display: -webkit-box;
	word-break: break-all;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.groom .item-index .item-text .info {
	justify-content: space-between;
	align-items: flex-end;
	padding: 0 10rpx;
}

.groom .item-index .item-text .info .old-price {
	font-size: 22rpx;
	color: rgba(0, 0, 0, 0.6);
	margin-left: 10rpx;
	margin-bottom: 4rpx;
	text-decoration: line-through;
}

.groom .item-index .item-text .info .new-price {
	font-size: 26rpx;
	font-weight: bold;
	color: #ff2f2f;
}

.groom .item-index .item-text .info image {
	width: 48rpx;
	height: 48rpx;
	display: block;
}

.groom .more-meal {
	color: #18e6c9;
	font-size: 28rpx;
	font-weight: bold;
	text-align: center;
	margin-top: 20rpx;
}

.groom .item-index {
	width: 326rpx;
	/* height: 470rpx; */
	overflow: hidden;
	margin: 15rpx 0;
}

.groom .item-img {
	text-align: center;
}

.groom .item-index .item-img image {
	width: 275rpx;
	height: 275rpx;
	margin: 0 auto;
}

.groom .item-index .item-text .info {
	justify-content: space-between;
	padding-right: 30rpx;
}

.item-box-index-tip {
	font-size: 20rpx;
	color: #ff2f43;
	margin: 0 10rpx;
}

.item-box-index-tip-text {
	border-radius: 6rpx;
	border: 1px solid #ff2f43;
	padding: 0 10rpx;
}

.background-left {
	width: 240rpx;
	height: 4rpx;
	background: linear-gradient(to left, #1794fd, #fff);
}

.background-right {
	width: 240rpx;
	height: 4rpx;
	background: linear-gradient(to right, #1794fd, #fff);
}

.border_bottom_one_hunderd {
	width: 100%;
	height: 150rpx;
	background: #f6f6f6;
}

.last-buy-order {
	width: calc(100% - 60rpx);
	height: 120rpx;
	left: -100%;
	background: white;
	position: fixed;
	bottom: 10rpx;
	margin: 0 30rpx;
	border-radius: 10rpx;
	transition: all 0.3s linear;
	background: linear-gradient(77deg, rgba(28, 145, 240, 1), rgba(71, 167, 245, 1));
	box-shadow: 0px 4px 33px 3px rgba(0, 0, 0, 0.16);
}

.last-buy-order-show {
	left: 0;
}

.last-buy-order-inner {
	width: calc(100% - 46rpx);
	box-sizing: border-box;
	padding: 15rpx 20rpx;
	display: flex;
	align-items: center;
}

.one-more-order {
	margin-left: 30rpx;
	font-size: 28rpx;
	width: 148rpx;
	height: 42rpx;
	line-height: 42rpx;
	text-align: center;
	color: #fff;
	border: 1px solid #fff;
	border-radius: 40rpx;
}

.one-more-order-close {
	width: 46rpx;
	height: 46rpx;
	background: rgba(0, 0, 0, 1);
	opacity: 0.05;
	border-radius: 0 10rpx 0 100%;
}

.one-more-order-close-show {
	left: calc(100% - 60rpx);
	bottom: 85rpx;
}

.time-limit-buy {
	width: 34rpx;
	height: 34rpx;
	line-height: 34rpx;
	text-align: center;
	background: #333;
	color: #fff;
	border-radius: 8rpx;
	font-size: 20rpx;
}

.last-buy-order-bottom {
	position: unset;
	margin-bottom: 10rpx;
	box-shadow: 0px 4px 33px 3px #f6f6f6;
}

.one-use-bucket {
	color: #999;
	padding: 0 10rpx;
	background: #f5f5f5;
	border-radius: 6rpx;
}

.pop-label-sign {
	width: 100rpx;
	padding: 3rpx 0;
	border: 1px solid #ff2f43;
	color: #ff2f43;
	text-align: center;
	font-size: 20rpx;
	border-radius: 6px;
	margin: 5rpx 0;
}

.today-buy-label {
	position: absolute;
	top: 0;
	background: red;
	font-size: 20rpx;
	padding: 5rpx 10rpx;
	border-bottom-right-radius: 6px;
	border-bottom-left-radius: 6px;
	color: #fff;
}


/* end by yanxingwang */

/* new add css */

.new-other {
	padding: 20rpx 2rpx;
	flex-wrap: wrap;
	/* height: 296rpx;
width: 220rpx; */
	position: relative;
	background: #fff;
	border-radius: 10rpx;
	font-size: 26rpx;
	/* margin: 5rpx; */
	width: 326rpx;
	height: 440rpx;
	overflow: hidden;
	margin: 12rpx 0;
}

.new-other .new_nav_right_items_detail_image {
	width: 100%;
	height: auto;
	text-align: center;
}

.new-other .new_nav_right_items_detail_image image {
	width: 275rpx;
	height: 275rpx;
	margin: 0 auto;
}

.shadowbg {
	position: fixed;
	left: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, .5);
  z-index: 999;
}

.noticebg {
	width: 100vw;
	height: 1000rpx;
	position: fixed;
	top: 56%;
	/* left: -3%; */
	transform: translate(0, -50%);
	background: url('https://waterstation.com.cn/szm/szmc/images/messageInform.png');
	background-size: 100% 100%;
}
.noticebg11 {
	width: 100vw;
	height: 1000rpx;
	position: fixed;
	top: 56%;
	transform: translate(0, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.noticecontent {
	padding: 0 130rpx;
	color: 32rpx;
	color: #FFFFFF;
	font-weight: 400;
	line-height: 40rpx;
	margin-top: 260rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 10;
	overflow: hidden;
}

.activitybg {
	width: 100vw;
	height: 862rpx;
	position: fixed;
	top: 56%;
	transform: translate(0, -50%);
	background: url('https://waterstation.com.cn/szm/szmc/images/messageActivity.png');
	background-size: 100% 100%;
}

.activitycontent {
	padding: 0 192rpx;
	color: #E46B00;
	font-size: 32rpx;
	font-weight: 400;
	line-height: 40rpx;
	margin-top: 225rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 5;
	overflow: hidden;
}

.pingbi {
	position: absolute;
	width: 229rpx;
	height: 65rpx;
	top: 165rpx;
	right: 20rpx;
	border: 2rpx solid rgba(99, 99, 99, 1);
	border-radius: 33rpx;
	opacity: .65;
	background: rgba(99, 99, 99, 0.3);
	color: #999999;
	font-size: 30rpx;
	line-height: 65rpx;
	text-align: center;
	font-weight: 400;
}

.redNumTips {
  position: absolute;
  min-width: 30rpx;
  height: 30rpx;
  background-color: #ff2f43;
  color: white;
  font-size: 20rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 30rpx;
  top: 0rpx;
  right: 15rpx;
}