<view class='content'>
    <!-- 输入框（表格） -->
    <view class='{{(interval?"pay_number":"pay_number_interval")}}  {{focus_class?"get_focus":""}}' catchtap="set_focus" style='width:{{width}};height:{{height}};'>
      <view class='{{focus_class?(interval?"get_focus_dot":"get_focus_dot_interval"):(interval?"password_dot":"password_dot_interval")}} {{index==0?"noBorder":""}}' wx:for="{{value_num}}" wx:key="{{index}}">
        <view wx:if="{{(value_length==item-1)&&focus_class}}" class="cursor"></view>
        <view wx:if="{{value_length>=item}}" class="{{see?'':'dot'}}">{{see?val_arr[index]:""}}</view>
      </view>
    </view>

    <!-- 输入框（隐藏） -->
    <input value="{{input_value}}" focus="{{get_focus}}" maxlength="6" type="number" class='input_container' placeholder="" bindinput="get_value" bindfocus="get_focus" bindblur="blur" />
  </view>