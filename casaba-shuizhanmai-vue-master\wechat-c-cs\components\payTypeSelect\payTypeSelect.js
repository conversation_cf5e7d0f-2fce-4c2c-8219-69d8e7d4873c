// components/payTypeSelect/payTypeSelect.js
import config from '../../config.js';
import util from '../../utils/util.js';
var app = getApp();
Component({
  lifetimes: {
    attached: function() {
      // 在组件实例进入页面节点树时执行
      let sexBoxType = [];
	  
	  let payTypeList = [] ;

      // console.log(util.judgeModel('spdk') == 1) 查询用户是否有开启月付的权限
      if (this.data.sexBoxType == 1) {
        // 显示的支付方式
        sexBoxType.push('微信支付');
		
        payTypeList.push({
          text:'微信支付',
          img:'/images/weixinzhifu.png'
        });
		
        if(util.judgeModel('yf')) {
          sexBoxType.push('月结付款');
          payTypeList.push({
            text:'月结付款',
            img:'/images/yuezhifu.png'
          });
		  
        } else {
			
          // sexBoxType.push('货到付款');
          //   payTypeList.push({
          //     text:'货到付款',
          //     img:'/images/huodaofukuan.png'
          //   });
        }
		
        if(util.judgeModel('yhzz')) {
          sexBoxType.push('银行转账');
		  
          payTypeList.push({
            text:'银行转账',
            img:'/images/zujinyuefu.png'
          });
		  
         }
      } else if (this.data.sexBoxType == 2) {
		  
        sexBoxType.push('微信支付');
        payTypeList.push({
          text:'微信支付',
          img:'/images/weixinzhifu.png'
        });
        // sexBoxType.push('货到付款');
        // payTypeList.push({
        //   text:'货到付款',
        //   img:'/images/huodaofukuan.png'
        // });
		
        if (util.judgeModel('yhzz')) {
          sexBoxType.push('银行转账');
		  
		  payTypeList.push({
		  	text:'银行转账',
		  	img:'/images/zujinyuefu.png'
		  });
		  
        }
      } else if (this.data.sexBoxType == 3) {
        // 根据订单支付方式来选择押桶金支付方式
        if (this.data.orderPayType == '月结付款' || this.data.orderPayType == '水票支付') {
          sexBoxType.push('微信支付', '银行转账')
        } else if (this.data.orderPayType == '银行转账') {
          sexBoxType.push('银行转账')
        } else if (this.data.orderPayType == '货到付款') {
          sexBoxType.push('货到付款')
        } else if (this.data.orderPayType == '微信支付') {
          sexBoxType.push('微信支付')
        }
      } else if (this.data.sexBoxType == 4) {
        sexBoxType.push('线下微信');
        sexBoxType.push('线下支付宝');
        sexBoxType.push('现金');
        if (util.judgeModel('yhzz')) {
          sexBoxType.push('银行转账');
        }
      }else if(this.data.sexBoxType == 5){ //支付水票活动
		  sexBoxType.push('微信支付');
		  
		  payTypeList.push({
		  	text:'微信支付',
		  	img:'/images/weixinzhifu.png'
		  });
		  
	  }
	  
      let shopInfo = wx.getStorageSync('shopInfo');
      this.setData({
		payTypeList:payTypeList || [],
        sexBox: sexBoxType,
        bankCard: shopInfo.szmCStore.bankCard,
        openingBank: shopInfo.szmCStore.openingBank,
		companyTitle: shopInfo.szmCStore.companyTitle
      });
	  
	  
	  
	  
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  /**
   * 组件的属性列表
   */
  properties: {
    payType: {
      type: String,
      value: "",
      observer: function(newVal, oldVal) {
		  
		let that = this ;
		
		let _sexBoxType = that.data.sexBoxType ;
		if(_sexBoxType == 1 || _sexBoxType == 2 || _sexBoxType == 5){
			let _payTypeList = that.data.payTypeList || [] ;
			let len = _payTypeList.length ; 
			for(var ii=0;ii<len;ii++){
				if(newVal == '水票支付'){
					_payTypeList[ii].disabled = true ;
				}else{
					_payTypeList[ii].disabled = false ;
				}
			}
			
			that.setData({
				payTypeList:_payTypeList
			});	
		}
		
      }
    },
    orderPayType: {
      type: String,
      value: "",
      observer: function(newVal, oldVal) {

      }
    },
    showBank: {
      type: Boolean,
      value: false,
      observer: function(newVal, oldVal) {

      }
    },
    sexBoxType: {
      type: Number,
      value: 0,
      observer: function(newVal, oldVal) {

      }
    },
    padding: {
      type: String,
      value: "",
      observer: function(newVal, oldVal) {

      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
	payTypeList:[],  
    sexBox: [],
    bankCard: "", //银行卡号 
    openingBank: "", //开户行
	companyTitle: "",
    showButton: true, //控制弹出，防止弹出两次
  },

  /**
   * 组件的方法列表
   */
  methods: {
	
	selectPayType:function(e){
		let that = this;
		that.triggerEvent('select', {
		  payType:e.detail.value
		});
	},  
	selectSex() {
      let that = this;
      if (that.data.payType == '水票支付') {
		
		//屏蔽不可用
		/**
		let _payTypeList = that.data.payTypeList || [] ;
		let len = _payTypeList.length ; 
		for(var ii=0;ii<len;ii++){
			_payTypeList[ii].disabled = true ;
		}
		that.setData({
			payTypeList:_payTypeList
		});
		 **/
		  
        return //util.showText('除押桶金，订单支付0元，不可选择其他支付');
      }
      if (that.data.showButton) {
        wx.showActionSheet({
          itemList: that.data.sexBox,
          success: function(res) {
            if (!res.cancel) {
              that.triggerEvent('select', {
                payType: that.data.sexBox[res.tapIndex]
              });
            }
          }
        });
        that.setData({
          showButton: false
        })
      }
      setTimeout(() => {
        that.setData({
          showButton: true
        })
      }, 1000)
    },
  }
})