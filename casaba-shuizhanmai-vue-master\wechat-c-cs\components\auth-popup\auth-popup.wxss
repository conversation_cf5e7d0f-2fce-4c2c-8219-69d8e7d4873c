.page-popup{
	min-height:100vh;
	position:fixed;
  z-index:9998;
  
}

.page-popup,
.page-popup-mask{
	width:100%;
	top:0;
	bottom:0;
	left:0;
}

.page-popup-mask {
	position:absolute;
	right:0;
	background:rgba(0,0,0,.7);
	z-index:11
}


.page-popup-content {
	position:absolute;
	background:#fff;
	padding:58rpx 46rpx;
	box-sizing:border-box;
	border-radius:36rpx 36rpx 0 0
}

.page-popup-content .title{
	position:relative;
	font-size:42rpx;
	color:#333333;
	font-family:PingFang-SC-Bold;
  margin-bottom:62rpx;
  font-weight: bold;
}

.page-popup-content .title .top-close{
  position:absolute;
  right:0;
  top:0;
  width:69rpx;
  text-align:right;
}

.page-popup-content .title .top-close wx-image{
  width:22rpx;height:22rpx
}


.page-popup-content .context{
  font-size:26rpx;
  line-height: 42rpx;
  color:#787878; 
  font-family:PingFang-SC-Medium;
  font-weight: 400;
  margin-bottom:62rpx;
}

.page-popup-content .context .sub-title{
  display: block;
  margin-bottom:20rpx;
}

.page-popup-content .context .bold-txt{
  font-weight: bold;
  color:#000;
}

.page-popup-content .context .unline-txt{
  font-weight: bold;
  color:#000;
  text-decoration: underline;
}

.page-popup-content .context .potal{
  color:#1a5eb3
}

.page-popup-content .tip{
	font-size:28rpx;
	color:#24272c;
	font-family:PingFangSC-Regular;
	margin-bottom:41rpx;
}

.page-popup-content .tip .link-txt{
  color:#3492FE;
}



.page-popup-content .btn-box{
	display:-webkit-flex;
	display:flex;
	-webkit-justify-content:center;
	justify-content:center
}

.page-popup-content .btn-box .btn{
	width:266px;
	height:85rpx;
	line-height:85rpx;
	text-align:center;
	font-size:14px;
	border-radius:42rpx
}

.page-popup-content .btn-box .btn.disagree{
	margin-right:50px;
	border: 1px solid #3392FE; 
	color:#3392FE;
}


.page-popup-content .btn-box .btn.agree{
	background-color:#3392FE;
	color:#fff
}

.page-popup-bottom{
	bottom:0;
	width:100%;
	left:0;
	right:0;
	z-index:12
}