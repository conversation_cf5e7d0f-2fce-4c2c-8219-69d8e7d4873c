@import "../../app.wxss";

.invoice-information-index {
  padding: 0 20rpx;
}

.invoice-information-index .close {
  width: 34rpx;
  height: 34rpx;
  background: #f5f5f5;
  border-radius: 50%;
}

.margin-right-button {
  margin-right: 60rpx;
}

.invoice-information-type {
  padding: 10rpx 30rpx;
  background: #f2f2f2;
  border-radius: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-up-type {
  padding: 10rpx 50rpx;
  background: #f2f2f2;
  border-radius: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.choose {
  color: #3392fe;
  border: 2rpx solid #3392fe;
  background: #e0eefd;
}

.invoice-title {
  width: 180rpx;
  flex-shrink: 0;
}

.invoice-info-button {
  font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 28rpx;
color: #FFFFFF;
  background: #3392fe;height: 88rpx;
  border-radius: 16rpx;
  line-height: 88rpx;
  text-align: center;
  margin-top: 60rpx;
  margin-bottom: 30rpx;
}

.show-title-tip {
  position: absolute;
  width: 350rpx;
  box-sizing: border-box;
  padding: 0 26rpx;
  background: #FFFFFF;
  left: 200rpx;
  box-shadow: 0 0 16rpx 0 rgba(37, 46, 62, 0.14);
  font-size: 24rpx;
  z-index: 1000;
}
