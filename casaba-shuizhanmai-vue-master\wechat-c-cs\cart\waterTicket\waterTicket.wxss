page {
  background: #fff;
}

.cartMain {
  padding-bottom: 160rpx;
  padding-top: 40rpx;
}

.tips {
  font-size: 30rpx;
  color: #888;
  padding: 24rpx;
}

.cart_main {
  border-radius: 15rpx;
  /* width: 76%; */
  width: 568rpx;
  min-height: 210rpx;
  padding: 0 30rpx;
  background: #333;
  justify-content: space-between;
}

.cart-image {
  width: 212rpx;
  height: 159rpx;
  padding: 15rpx 10rpx;
}

.cart-box {
  width: 100%;
  height: auto;
  padding: 12rpx 0;
  /* margin-top: 15rpx; *//* padding-left: 60rpx; */
}

.icon {
  margin: 0rpx 20rpx;
}

.icon image {
  width: 44rpx;
  height: 44rpx;
}

.no-use {
  font-size: 24rpx;
  color: #bebebe;
  margin-left: 16%;
}

.left .dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background: #333;
  margin: 7rpx auto;
}

.left .i2 {
  margin: 10rpx auto;
}

.left .img {
  width: 32rpx;
  height: 46rpx;
}

.left {
  font-size: 18rpx;
  color: #333;
  text-align: center;
}

.left .dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
}

.left {
  width: 200rpx;
}

.left .num {
  margin-left: 5rpx;
}

.right {
  margin-left: 5%;
  font-size: 28rpx;
  color: #333;
  width: 70%;
  line-height: 38rpx;
}

.right .line {
  width: 64rpx;
  height: 2rpx;
  background: #333;
  display: block;
  margin: 0 10rpx;
}

.rule {
  display: -webkit-box;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.botton {
  background: #fff;
  position: fixed;
  bottom: 0rpx;
  width: 100%;
  padding: 30rpx;
  box-shadow: 0 -5rpx 10rpx rgba(0, 0, 0, 0.4);
}

.botton-box {
  width: 90%;
  left: 5%;
  border-radius: 15rpx;
  background: #fff;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.4);
  background: linear-gradient(to right, #1794fd, #68ddff);
  color: #fff;
  font-size: 36rpx;
  height: 88rpx;
  text-align: center;
  line-height: 88rpx;
}

.no {
  padding-top: 150rpx;
  font-size: 30rpx;
  color: #888;
  text-align: center;
}

.no image {
  width: 374rpx;
  height: 303rpx;
}

/* 模态框 */

.modal-mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
  overflow: hidden;
  z-index: 9000;
}

.modal-dialog {
  box-sizing: border-box;
  width: 85%;
  padding: 40rpx 60rpx;
  overflow: hidden;
  position: fixed;
  top: 10%;
  bottom: 15%;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 9999;
  background: white;
  border-radius: 10rpx;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.app_btn {
  height: 75rpx;
  line-height: 75rpx;
  font-size: 32rpx;
}

.app_btn1 {
  height: 75rpx;
  line-height: 75rpx;
  font-size: 32rpx;
  background: transparent;
  color: #1794fd;
  border: 2rpx solid #1794fd;
}
