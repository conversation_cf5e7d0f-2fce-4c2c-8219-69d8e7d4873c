// cart/index/index.js
import {
  windowHeight,
  windowWidth,
} from '../../utils/common.js';
import config from '../../config.js';
import util from '../../utils/util.js';
import variable from '../../utils/variable.js';
import user from '../../utils/request/user.js'
var app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    drainageUser: {},
    drainage: {},
    drainageuserid: '',
    drainageid: '',
    store: {},
    isUpPrice: 1,
    upPrice: 0,
    yunfei: 0,
    isturn: false,
    showModalAddress: false,
    canuserticket: [],
    ticketPrice: 0,
    ticketUserId: 0,
    discount: 0,
    is_select: [],
    sureUseTicket: [],
    cartItems: [],
    originalCartItems: [], // 原水票数组
    windowHeight: windowHeight(),
    showModalStatus: false,
    is_loading: false,
    // 输入框参数设置
    inputData: {
      input_value: "", //输入框的初始内容
      value_length: 0, //输入框密码位数
      isNext: false, //是否有下一步的按钮
      get_focus: true, //输入框的聚焦状态
      focus_class: true, //输入框聚焦样式
      value_num: [1, 2, 3, 4, 5, 6], //输入框格子数
      height: "84rpx", //输入框高度
      width: "504rpx", //输入框宽度
      see: false, //是否明文展示
      interval: true, //是否显示间隔格子
      discountFlag: 0, //是否有水票可用
    },
    is_depositPay: 0, //是否显示密码输入框
    payType: "微信支付",
    is_info: false,
    moreMeal: false, //推荐套餐模板 是否显示更多按钮
    meal: [],
    mealRe: [],
    is_showCar: 'num', // num 数量-结算页 car 购物车- liji 立即抢购-首页
    // 总价 total
    total: '', // 订单实际付款总价
    orderTotalPrice: '', // 订单总金额，包含水票抵扣的金额
    goodsTotal: "", // goodsTotal 商品总价
    groomsTotal: "", // 套餐总价
    sureUseTicket: '', //使用的水票
    // 运费 freight
    freight: 0,
    useTicketNum: 0,
    useTicketNumTitle: '请选择您要使用的水票',
    cartIds: [],
    is_submit: true,
    totalNum: 0, //总数量
    hasActivityGoods: false, //是否有活动商品
    activityGoodsPostage: 0, //活动商品邮费
    is_blur: 0,
    onFocus: false,
    deduct: '', // 派单员提成
    isUserTicket: 1,
    cartItems: '',
    usableTicket: 1,
    freightAndUpfloor: 0,
    isShowYF: false, //是否显示月付
    imgUri: app.imgUri,
    // 新手蒙版指引
    showMaskGuide: false,
    // 点击显示下一张图片
    showGuideImage: 1,
    random: 0,
    szmCMaskId: '', // 蒙层Id
    showModalBillStatus: false,
    chooseDrawBill: false,
    bucketMoneyTotal: "0.00",
    bucketMoneyPayTypeList: [],
    bucketMoneyPayType: '',
    paymentDescriptionFirst: '',
    paymentDescriptionSecond: '',
    paymentDescriptionThird: '',
    invoiceOrder: null,
    returnBackBucketList: [],
    canOpenBill: false, // 开发票权限
    canBucket: false, //线上押桶权限
    showCloseTip: false, // 显示店铺休息
    checkboxselect: 1,
  },

  goSettlement(e) {
    // 体验用户，提示登录
    if (app.userId && app.userId == variable.userId) {
      util.gotoLogin();
      return;
    }
    // 体验店铺，提示登录
    if (app.storeId && app.storeId == variable.storeId) {
      util.gotoChooseShop();
      return;
    }
    let that = this,
      cartItems = that.data.cartItems,
      o = [],
      is_info = that.data.is_info,
      bucketMoneyPayTypeList = [],
      paymentDescriptionFirst = '',
      paymentDescriptionSecond = '',
      paymentDescriptionThird = '';
    if (!is_info) {
      wx.showModal({
        title: '温馨提示',
        content: '请先添加收货信息，点击确定前往设置！',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/user/address/address?pageType=set',
            })
          }
        }
      })
      return;
    }
    var login = wx.getStorageSync('login'),
      payType = that.data.payType;
    for (var i in cartItems) {
      var item = '';
      if (cartItems[i].selected == true) {
        if (cartItems[i].source == 0) {
          item = {
            'skuId': cartItems[i].cartShop.skuId,
            'state': cartItems[i].source
          }
        } else if (cartItems[i].source == 1) {
          item = {
            'skuId': cartItems[i].cartShopGroupList.groupId,
            'state': cartItems[i].source
          }
        } else {
          item = {
            'skuId': cartItems[i].cartShop.skuId,
            'state': cartItems[i].source
          }
        }
        o.push(item);
      }
    }

    // 根据订单支付方式来选择押桶金支付方式
    if (that.data.payType == '月结付款' || that.data.payType == '水票支付') {
      bucketMoneyPayTypeList.push({
        name: '微信支付',
        imgUri: '/images/weixinzhifu.png'
      })
      if (util.judgeModel('yhzz')) {
        bucketMoneyPayTypeList.push({
          name: '银行转账',
          imgUri: app.imgUri + '/images/huo-dap-blue.png'
        })
      }
      paymentDescriptionFirst = '支付完成后，系统自动下单成功！';
    } else if (that.data.payType == '银行转账') {
      bucketMoneyPayTypeList.push({
        name: '银行转账',
        imgUri: app.imgUri + '/images/huo-dap-blue.png'
      })
      paymentDescriptionFirst = '确认付款后，系统自动下单成功！';
      paymentDescriptionSecond = '请尽快线下转账，并通知商户收款。';
    } else if (that.data.payType == '货到付款') {
      bucketMoneyPayTypeList.push({
        name: '货到付款',
        imgUri: '/images/huodaofukuan.png'
      })
      paymentDescriptionFirst = '支付完成后，系统自动下单成功！';
    } else if (that.data.payType == '微信支付') {
      bucketMoneyPayTypeList.push({
        name: '微信支付',
        imgUri: '/images/weixinzhifu.png'
      })
      paymentDescriptionFirst = '支付完成后，系统自动下单成功！';
    }
    if (that.data.bucketMoneyTotal > 0) {
      if ((that.data.shouldPaytotal - that.data.discount) > 0) {
        paymentDescriptionThird = '订单金额+押桶金';
      } else {
        paymentDescriptionThird = '押桶金';
      }
      if (that.data.payType == '月结付款') {
        paymentDescriptionThird = '押桶金';
      }
    } else {
      paymentDescriptionThird = '订单金额';
      if (that.data.payType == '月结付款') {
        bucketMoneyPayTypeList = [{
          name: '月结付款',
          imgUri: '/images/zujinyuefu.png'
        }]
      }
    }
    if (that.data.payType == '水票支付' && that.data.total == '0.00') {
      that.payMoney();
    } else {
      var animation = wx.createAnimation({ // 显示遮罩层
        duration: 200,
        timingFunction: "linear",
        delay: 0
      })
      that.animation = animation
      animation.translateY(300).step()
      that.setData({
        animationData: animation.export(),
      })
      setTimeout(function () {
        animation.translateY(0).step()
        that.setData({
          animationData: animation.export(),
          showModalStatus: true,
          bucketMoneyPayTypeList: bucketMoneyPayTypeList,
          bucketMoneyPayType: bucketMoneyPayTypeList[0].name,
          paymentDescriptionFirst: paymentDescriptionFirst,
          paymentDescriptionSecond: paymentDescriptionSecond,
          paymentDescriptionThird: paymentDescriptionThird
        })
      }.bind(that), 200);
    }
  },
  hideModal: function () {
    // 隐藏遮罩层
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    this.animation = animation
    animation.translateY(300).step()
    if (this.data.showModalStatus) {
      this.setData({
        animationData: animation.export(),
      })
      setTimeout(function () {
        animation.translateY(0).step()
        this.setData({
          animationData: animation.export(),
          showModalStatus: false,
        })
      }.bind(this), 200)
    } else {
      this.setData({
        showModalBillStatus: false
      })
    }
  },

  onShowText(e) {
    this.setData({
      is_blur: 0,
      onFocus: false,
    })
  },
  onShowTextarea(e) {
    this.setData({
      is_blur: 1,
      onFocus: true,
    })
  },
  forgetPwd(e) {
    wx.navigateTo({
      url: '/user/mySet/setKeyt/setKeyt',
    })
  },
  // 当组件输入数字6位数时的自定义函数
  valueSix(e) {
    var _this = this;
    var code = e.detail;
    _this.setData({
      is_submit: false,
    })
    var login = wx.getStorageSync('login');
    var shopInfo = wx.getStorageSync('shopInfo');
    var userId = app.userId;
    var freight = shopInfo.szmCStore.storeShippingfee == null ? 0 : shopInfo.szmCStore.storeShippingfee;
    //console.log(_this.data.addressInfo, "我是地址")

    wx.request({
      url: config.walletPayGoods,
      data: {
        "payCode": code, //支付密码
        "price": Number(_this.data.total).toFixed(2), // 实际付款
        orderNum: _this.data.walletPay,
        userId: app.userId,
      },
      method: "POST",
      success(res) {
        //console.log(res);
        if (res.data.code == 1) {
          wx.showToast({
            title: '支付中...',
          })
          wx.removeStorageSync('sureUseTicket');
          wx.removeStorageSync('ticketList');
          setTimeout(function () {
            wx.redirectTo({
              url: '/cart/paySuccess/paySuccess?money='+ (_this.data.payType == '微信支付' ? _this.data.total : 0),
              success(res) {
                _this.setData({
                  is_submit: true,
                })
              }
            })
          }, 500)
        } else {
          wx.showToast({
            title: res.data.data,
            icon: 'none'
          })
        }
      },
      fail(res) {

      }
    })
    //console.log(code);
  },
  // 关闭押金支付密码输入框
  closeDepositPay: function () {
    var _this = this;
    wx.showToast({
      title: '支付已取消',
      icon: 'none',
      success(res) {
        setTimeout(function () {
          _this.setData({
            is_depositPay: 0
          })
          wx.redirectTo({
            url: '/user/myOrder/myOrder',
          })
        }, 300)
      }
    })
  },
  // // 去结算
  payMoney: function (e) {
    var _this = this;
    var payType = _this.data.payType;
    var login = wx.getStorageSync('login');
    if (_this.data.addressInfo == undefined || _this.data.addressInfo == '') {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      })
      return;
    }
    _this.setData({
      showModalStatus: false
    })

    app.request({
      url: config.deduct,
      data: {
        storeId: app.storeId,
        addressId: _this.data.addressInfo.addressId,
      },
      success(res) {
        if (res.data.code == 1) {
          _this.setData({
            deduct: res.data.data
          })
          // 查询结算中的下架商品
          util.ajax(config.issoldout, {
            list: JSON.stringify(_this.data.cartIds)
          }, resList => {
            if (resList.data.code == 1 && resList.data.data.length > 0) {
              let cartItems = _this.data.cartItems,
                list = resList.data.data;
              list.forEach(x => {
                cartItems.forEach(y => {
                  if (x == y.cartId) {
                    y.showDown = 1;
                  }
                })
              })
              util.showText("商品已下架");
              _this.setData({
                cartItems: cartItems
              });
            } else {
              if (_this.data.is_submit) {
                if (payType == '余额支付') {
                  _this.unOnlinePay(4);
                } else if (payType == '微信支付') {
                  _this.unOnlinePay(1)
                } else if (payType == '货到付款') {
                  _this.unOnlinePay(2);
                } else if (payType == '月结付款') {
                  _this.unOnlinePay(3);
                } else if (payType == '水票支付') {
                  _this.unOnlinePay(6);
                } else if (payType == '银行转账') {
                  _this.unOnlinePay(7);
                }
              }
            }
          })
        } else {
          wx.showToast({
            title: res.data.data,
            icon: 'none'
          })
        }
      }
    })

  },

  // 线下支付 货到付款 & 月付
  unOnlinePay(payId) {
    let _this = this,
      login = wx.getStorageSync('login'),
      shopInfo = wx.getStorageSync('shopInfo'),
      userId = app.userId,
      freight = shopInfo.szmCStore.storeShippingfee,
      uri = '',
      invoiceOrder = this.data.invoiceOrder
    _this.setData({
      is_submit: false,
      // is_loading: true
    });
    wx.showLoading({
      title: '支付中',
      mask: true
    })

    //如果是活动产品 且有邮费
    if (_this.data.hasActivityGoods && _this.data.activityGoodsPostage > 0) {
      freight = _this.data.activityGoodsPostage;
    }


    if (payId == 1) {
      uri = config.wxchatPay
    } else if (payId == 4) { // 余额支付
      if (login[0].paymentCode == null) {
        wx.showModal({
          title: '温馨提示',
          content: '您尚未设置钱包密钥，点击确定前往个人中心设置或选择其他支付方式！',
          success(res) {
            if (res.confirm) {
              wx.navigateTo({
                url: '/user/mySet/setKeyt/setKeyt',
              })
            }
          }
        })
        return;
      }
      if (Number(login[0].money) < Number(_this.data.total)) {
        wx.showModal({
          title: '温馨提示',
          content: '您的钱包余额不足，请前往个人中心充值或选择其他支付方式！',
          success(res) {
            if (res.confirm) {
              wx.navigateTo({
                url: '/user/mySet/setKeyt/setKeyt',
              })
            }
          }
        })
        return;
      }
      uri = config.unOnlinePay;
    } else {
      
      if (_this.data.total != '0.00' && _this.data.bucketMoneyTotal == '0.00' && payId == 6) {
        payId = 1;
        uri = config.wxchatPay
      } else {
        uri = config.unOnlinePay;

      }
    }
    if (invoiceOrder) {
      invoiceOrder.invoiceMoney = this.data.invoiceMoney;
    }
    // 去除地址中的前缀中的空格
    let userAddressBefore = _this.data.addressInfo.province + _this.data.addressInfo.city + _this.data.addressInfo.area + _this.data.addressInfo.street;
    userAddressBefore = userAddressBefore.replace(/\s/g, "");
    let paramJson = {
      "upPrice": _this.data.upPrice, //上楼费
      "yunfei": _this.data.yunfei, //上楼费
      "drainageuserid": _this.data.drainageuserid, //上楼费
      "drainageid": _this.data.drainageid, //上楼费
      "cartIds": _this.data.cartIds, //购物车id array
      "createIden": userId, //用户ID str
      "userId": userId, //用户ID 
      "ticketUserId": this.data.ticketUserId,
      "ticketPrice": this.data.ticketPrice,
      "freightPayable": freight == null ? 0 : freight, //运费
      "incomeAddrId": _this.data.addressInfo.addressId, //收货id
      "userAddress": userAddressBefore + (_this.data.addressInfo.r1 == 0 ? " 家庭·" : ' 企业·') + (_this.data.addressInfo.r2 == 0 ? "无电梯·" : '有电梯·') + (_this.data.addressInfo.r3 == 8 ? '7楼以上 ' : _this.data.addressInfo.r3 + '楼 ') + (_this.data.addressInfo.r4 == null ? '' : _this.data.addressInfo.r4), // 用户地址
      "userContent": _this.data.remarks, // 用户留言
      "userName": _this.data.addressInfo.userName, // 用户姓名
      "userPhone": _this.data.addressInfo.telphoneOne, // 用户电话
      "zuobiao": _this.data.addressInfo.r5, // 用户电话
      "orderDiscounts": Number(_this.data.discount ? _this.data.discount : 0), // 优惠价格
      "orderMoney": Number(_this.data.total) + Number(_this.data.discount ? _this.data.discount : 0), // 订单原价
      "paymentModeId": payId, // 付款方式id
      "r1": Number(_this.data.total), // 实际付款
      "r2": app.storeId, // 商户id
      "storeId": app.storeId, // 商户id
      "r4": JSON.stringify(_this.data.r4Data), // 优惠方式
      "r5": _this.data.totalNum, // 总数量
      "smzCGroupOrderList": _this.data.smzCGroupOrderList, // 套餐列表
      "smzCOrderDetailsList": _this.data.smzCOrderDetailsList, // 普通商品 列表
      // 2020-01-13 add
      "bucketPayType": _this.data.bucketMoneyPayType == '微信支付' ? 0 : _this.data.bucketMoneyPayType == '银行转账' ? 5 : _this.data.bucketMoneyPayType == '货到付款' ? 6 : '', //押桶金付款方式
      "bucketPrice": _this.data.bucketMoneyTotal, // 桶的总金额
      "bucketPriceVos": _this.data.returnBackBucketList, //押桶的对象集合
      "isInvoice": _this.data.chooseDrawBill ? 1 : 0, //是否开发票
      "invoiceOrder": _this.data.chooseDrawBill ? _this.data.invoiceOrder : {} //发票信息
    }
    // console.log(JSON.stringify(paramJson))
    wx.request({
      url: uri,
      data: paramJson,
      method: "POST",
      success(res) {
        if (res.data.code == 1) {
          
          if (payId == 1) {
            if (Number(_this.data.total) <= 0) {

              wx.showToast({
                title: '支付中...',
              })
              wx.removeStorageSync('sureUseTicket');
              wx.removeStorageSync('ticketList');
              setTimeout(function () {
                wx.redirectTo({
                  url: '/cart/paySuccess/paySuccess?money='+  (_this.data.payType == '微信支付' ? _this.data.total : 0),
                  success(res) {
                    _this.setData({
                      is_submit: true,
                    })
                  }
                })
              }, 500)
            } else {
              // 订单微信支付
              _this.requestPaymentFun(res, 1);

            }
          } else if (payId == 4) {
            var inputData = 'inputData.get_focus',
              res = res.data.data
            _this.setData({
              is_depositPay: 1,
              [inputData]: true,
              walletPay: res
            });
          } else {
            // if(_this.data.total > 0) {

            if (res.data.data.nonceStr) {
              // 押桶金微信支付
              _this.requestPaymentFun(res, 2);
            } else {

              wx.showToast({
                title: '支付中...',
              })
              setTimeout(function () {
                wx.redirectTo({
                  url: '/cart/paySuccess/paySuccess?money='+  (_this.data.payType == '微信支付' ? _this.data.total : 0),
                  success(res) {
                    _this.setData({
                      is_submit: true,
                      // is_loading: false
                    })
                    wx.hideLoading();
                  }
                })
              }, 500)
            }
            // } else {

            // }
          }
        } else {
          _this.setData({
            is_submit: true,
            // is_loading: false
          })
          wx.hideLoading();
          util.showText(res.data.data);
        }
      },
      fail(res) {
        _this.setData({
          is_submit: true,
          // is_loading: false
        })
        wx.hideLoading();
      },
    })
  },
  // 唤起微信支付
  requestPaymentFun(res, type) {
    let _this = this;
    wx.requestPayment({
      timeStamp: res.data.data.timeStamp,
      nonceStr: res.data.data.nonceStr,
      package: res.data.data.package,
      signType: res.data.data.signType,
      paySign: res.data.data.paySign,
      success(data) {
        //console.log(data, 'data');
        if (data.errMsg == "requestPayment:ok") {
          wx.showToast({
            title: '支付中...',
          })
          wx.removeStorageSync('sureUseTicket');
          wx.removeStorageSync('ticketList');
          setTimeout(function () {
            wx.redirectTo({
              url: '/cart/paySuccess/paySuccess?money='+  (_this.data.payType == '微信支付' ? _this.data.total : 0),
              success(res) {
                _this.setData({
                  is_submit: true,
                })
              }
            })
          }, 500)
        }
      },
      fail(f) {
        if (f.errMsg == "requestPayment:fail cancel") {
          wx.showToast({
            title: '取消支付',
            icon: "none",
          })
          _this.setData({
            is_submit: true,
            // is_loading: false
          })
          wx.hideLoading();
          // 订单微信支付时，产生待支付订单
          if (type == 1) {
            setTimeout(function () {
              wx.redirectTo({
                url: '/user/myOrder/myOrder',
                success(res) {
                  _this.setData({
                    is_submit: true,
                  })
                }
              })
            }, 500)
          }
        }
      },
    })
  },
  getRemarks(e) {
    //console.log(e);
    var remarks = e.detail.value;
    this.setData({
      remarks: remarks
    })
  },
  payChange(e) {
    //console.log(e);
    let _this = this,
      index = e.detail.index,
      paymentDescriptionFirst = '',
      paymentDescriptionSecond = '';
    if (_this.data.bucketMoneyPayTypeList.length == 1) {
      return
    }
    if (index == 1) {
      paymentDescriptionFirst = '银行转账支付押桶金后，系统自动下单成功！';
      paymentDescriptionSecond = '订单金额依然为月结付款。';
    } else {
      paymentDescriptionFirst = '支付完成后，系统自动下单成功！';
    }
    _this.setData({
      bucketMoneyPayType: _this.data.bucketMoneyPayTypeList[index].name,
      paymentDescriptionFirst: paymentDescriptionFirst,
      paymentDescriptionSecond: paymentDescriptionSecond
    })
  },
  selectTicket(e) {
    // var _this = this;
    // var id = [];
    // var cartItems = wx.getStorageSync('cartItems');
    // for (var i in cartItems) {
    //   if (cartItems[i].selected && cartItems[i].cartShop != null) {
    //     id.push(cartItems[i].cartShop.skuId);
    //   }
    // }
    // if (_this.data.isCanTarget == 1) {
    //   if (_this.data.discount > 0) {
    //     if (util.judgeModel('spdk') == 1) {
    //       wx.navigateTo({
    //         url: '/cart/waterTicket/waterTicket?idArr=' + JSON.stringify(id) + '&total=' + Number(Number(_this.data.total) + Number(_this.data.discount))
    //       })
    //     } else {
    //       wx.navigateTo({
    //         url: '/cart/waterTicketOld/waterTicketOld?idArr=' + JSON.stringify(id) + '&total=' + Number(Number(_this.data.total) + Number(_this.data.discount))
    //       })
    //     }
    //   } else {
    //     if (util.judgeModel('spdk') == 1) {
    //       wx.navigateTo({
    //         url: '/cart/waterTicket/waterTicket?idArr=' + JSON.stringify(id) + '&total=' + _this.data.total
    //       })
    //     } else {
    //       wx.navigateTo({
    //         url: '/cart/waterTicketOld/waterTicketOld?idArr=' + JSON.stringify(id) + '&total=' + _this.data.total
    //       })
    //     }
    //   }
    // } else {
    //   wx.showModal({
    //     title: '温馨提示',
    //     content: '您暂无可用的优惠方式，可能您没有购买水票或者您购买的商品暂时不支持使用水票！',
    //     showCancel: false
    //   })
    // }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      isturn: options.isturn || 0,
      drainageuserid: options.drainageuserid || 0,
    })
    if(this.data.drainageuserid) {
      this.getdrainageuser();
    }
    var cartItems = wx.getStorageSync('cartItems'),
      login = wx.getStorageSync('login'),
      shopInfo = wx.getStorageSync('shopInfo'),
      goodsTotal = 0, // 普通商品总价
      goodsTotalOriginal = 0, // 普通商品原价
      groomsTotal = 0, //套餐数据总价
      groomsTotalOriginal = 0, //套餐数据总价
      smzCGroupOrderList = [], //套餐数据
      smzCOrderDetailsList = [], // 普通商品
      cartIds = [], // 购物车 id total
      totalNum = 0, //结算总数量
      isOnlyGroup = 1,
      isOnlyDiscouts = 1,
      userPayOnUpPrice = 0, // 客户付上楼费，计算方式修改
      totalSingleGroupNum = 0; // 商品数量，包括套餐中商品的数量
    wx.setStorageSync('ticketUserId', 0);
    wx.setStorageSync('ticketPrice', 0);

    var _actGoods = false;

    for (var i in cartItems) {
      if (cartItems[i].selected) {
        cartIds.push(cartItems[i].cartId);


        if (!_actGoods && cartItems[i].source === '5') {
          _actGoods = true;
        }

        if (cartItems[i].cartShop == null && cartItems[i].cartShopGroupList.skuNumber != '商品数量不足' && cartItems[i].cartShopGroupList.groupState == 0) {
          isOnlyDiscouts = 0;
          smzCGroupOrderList.push({
            groupOrderProductPrice: cartItems[i].cartShopGroupList.rulingPrice * cartItems[i].prouderNum,
            orderProductNum: cartItems[i].prouderNum,
            shopgroupId: cartItems[i].cartShopGroupList.groupId
          })
          groomsTotal += cartItems[i].cartShopGroupList.rulingPrice * cartItems[i].prouderNum;
          groomsTotalOriginal += cartItems[i].cartShopGroupList.originalPrice * cartItems[i].prouderNum;
          totalNum += cartItems[i].prouderNum;
          // 判断是否是桶装水
          cartItems[i].isBarreledWater = false
          cartItems[i].isBuckets = false
          cartItems[i].cartShopGroupList.list.forEach(x => {
            if (x.className == "桶装水" && x.buckType != 1) {
              cartItems[i].isBarreledWater = true
            }
            totalSingleGroupNum += (cartItems[i].prouderNum * x.skuNumber);
          });
        } else if (cartItems[i].cartShopGroupList == null && cartItems[i].cartShop.skuNumber != '商品数量不足' && cartItems[i].cartShop.skuState == 0) {
          isOnlyGroup = 0;
          if (cartItems[i].cartShop.marketPrice != null) {
            if (cartItems[i].cartShop.vipPrice != null) {
              isOnlyDiscouts = 1;
              // 0普通1限时2买赠3优惠
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].cartShop.marketPrice;
            } else {
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].prouderPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].cartShop.marketPrice;
            }
          } else {
            if (cartItems[i].cartShop.vipPrice != null) {
              isOnlyDiscouts = 1;
              // 0普通1限时2买赠3优惠
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
            } else {
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].prouderPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
            }
          }
          totalNum += cartItems[i].prouderNum;
          totalSingleGroupNum += cartItems[i].prouderNum;
          // 判断是否是桶装水
          if (cartItems[i].cartShop.className == "桶装水" && cartItems[i].cartShop.buckType != 1) {
            cartItems[i].isBarreledWater = true
          } else {
            cartItems[i].isBarreledWater = false
          }
          cartItems[i].isBuckets = false
        }

      }
    }
    userPayOnUpPrice = Number(Number(login[0].upFloorMoney) * Number(totalSingleGroupNum)).toFixed(2);
    if (shopInfo != null || shopInfo != '' || shopInfo != undefined) {
      this.setData({
        // freightAndUpfloor: Number(Number(shopInfo.szmCStore.storeShippingfee) + Number(login[0].upFloorMoney)).toFixed(2), //修改
        freightAndUpfloor: Number(userPayOnUpPrice).toFixed(2) // 客户付上楼费单价
      })
    }
    this.setData({
      isOnlyGroup: isOnlyGroup, // 是否只有套餐
      isOnlyDiscouts: isOnlyDiscouts, // 是否只有优惠
      cartItems: cartItems,
      goodsTotal: Number(goodsTotal).toFixed(2),
      goodsTotalOriginal: Number(goodsTotalOriginal).toFixed(2),
      goodsTotalDiscounts: Number(Number(goodsTotalOriginal) - Number(goodsTotal)).toFixed(2),
      groomsTotal: Number(groomsTotal).toFixed(2),
      groomsTotalOriginal: Number(groomsTotalOriginal).toFixed(2),
      groomsTotalDiscounts: Number(Number(groomsTotalOriginal) - Number(groomsTotal)).toFixed(2),
      cartIds: cartIds,
      totalNum: totalSingleGroupNum,
      hasActivityGoods: _actGoods, //是否活动商品
      total: (Number(goodsTotal) + Number(groomsTotal) + Number(shopInfo.szmCStore.storeShippingfee) + Number(userPayOnUpPrice)) < 0 ? 0 : (Number(goodsTotal) + Number(groomsTotal) + Number(shopInfo.szmCStore.storeShippingfee) + Number(userPayOnUpPrice)),
      balance: login[0].money,
      random: Math.ceil(Math.random() * 100000),
      smzCGroupOrderList: smzCGroupOrderList,
      smzCOrderDetailsList: smzCOrderDetailsList,
      //结算商品原总价
      totalOriginal: Number(Number(goodsTotalOriginal) + Number(groomsTotalOriginal)).toFixed(2),
      //结算商品应付总价
      shouldPaytotal: Number(Number(goodsTotal) + Number(groomsTotal) + Number(shopInfo.szmCStore.storeShippingfee) + Number(userPayOnUpPrice)).toFixed(2),
      // 优惠合计
      discountTotal: Number(Number(goodsTotalOriginal) + Number(groomsTotalOriginal) - Number(goodsTotal) - Number(groomsTotal)).toFixed(2),
      showDiscountTotal: Number(Number(goodsTotalOriginal) + Number(groomsTotalOriginal) - Number(goodsTotal) - Number(groomsTotal)).toFixed(2),
      isShowYF: util.judgeModel('yf'),
      canOpenBill: (util.judgeModel('pp') || util.judgeModel('zp')),
      canBucket: util.judgeModel('xsyt'),
    })
  },
  // 请选择个人信息
  chooseInfo: function (e) {
    this.setData({
      showModalAddress: false
    })
    wx.navigateTo({
      url: '/user/address/address?pageType=set',
    })
  },
  // 去 商家服务

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  // 优惠方式提示
  useDiscount() {
    let that = this;
    let _this = this;
    var idBox = [];

    var cartItems = wx.getStorageSync('cartItems');
    for (let i in cartItems) {
      if (cartItems[i].cartShop != null && cartItems[i].selected == true && (cartItems[i].cartShop.newSource == 0 || cartItems[i].cartShop.newSource == 3)) {
        idBox.push(cartItems[i].cartShop.skuId);
      }
    }
    // newSource //0普通1限时2买赠3优惠
    if (idBox && idBox.length > 0) {
      wx.request({
        url: config.isHaveTicket,
        data: {
          productModelId: idBox,
          userId: app.userId,
          storeId: app.storeId
        },
        method: "POST",
        success(res) {
          if (res.data.code == 1) {
            //console.log(res);
            let list = res.data.data;
            let num = 0;
            list.forEach(function (item) {
              // 显示水票总数 ，判断显示可使用水票总数量
              if (item.isUsable == 1) {
                num = num + Number(item.count);
              }
            })
            if (num) {
              that.setData({
                useTicketNumTitle: num + "张可用",
                discountFlag: 1,
                isCanTarget: 1,
              })
            } else {
              that.setData({
                useTicketNumTitle: '暂无可用',
                discountFlag: 0,
                isCanTarget: 0,
              })
            }
            let bbcartItems = [];
            list.forEach(x => {
              if (x.isUsable == 1) {
                bbcartItems.push(x)
              }
            })

            // 合并水票记录中相同的水票
            let newCartItems = _this.mergeListbbb(bbcartItems);
            _this.setData({
              originalCartItems: res.data.data
            })
            let is_select = [];
            for (let i = 0; i < newCartItems.length; i++) {
              let arr = {
                'selected': true
              };
              is_select.push(arr);
            }
            _this.setData({
              is_select: is_select
            })
            let originalCartItems = _this.data.originalCartItems, // 原始水票list
              data = [];

            for (var i in is_select) {
              if (is_select[i].selected) {
                originalCartItems.forEach(x => {
                  // 从合并集合中选取出对应的原始水票list中的对象
                  if ((x.waterCoupon == newCartItems[i].waterCoupon) && (x.price == newCartItems[i].price)) {
                    data.push(x);
                  }
                })
              }
            }
            _this.setData({
              sureUseTicket: data,
            })
            _this.setShuiPiao();
          }
        },
      })
    } else {

      that.setData({
        orderTotalPrice: Number(this.data.shouldPaytotal - this.data.ticketPrice) < 0 ? 0 :
          Number(this.data.shouldPaytotal - this.data.ticketPrice).toFixed(2),
        useTicketNumTitle: '暂无可用',
        discountFlag: 0,
        isCanTarget: 0,
      })
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let _this = this;
    var ticketPrice = wx.getStorageSync('ticketPrice');
    var ticketUserId = wx.getStorageSync('ticketUserId');
    this.setData({
      'ticketPrice': ticketPrice,
      'ticketUserId': ticketUserId,
    })
    _this.useDiscount();
    _this.getcanuseticket();
    // app.getToken();
    var login = wx.getStorageSync('login');
    if (login == null || login == '' || login == undefined) {
      app.getToken();
    } else {
      _this.setData({
        login: login
      })
    };

    if (!_this.data.addressInfo) {
      _this.defaultAddress();
    } else {
      _this.setData({
        is_info: true
      })
      // _this.checkAddress(_this.data.addressInfo.addressId);
      if(_this.data.addressInfo) {
        _this.getUpPrice(_this.data.addressInfo.addressId);
      }
    }
    app.bindOpenId();
    // 重新获取店铺信息
    _this.homeStore();
    if (!_this.data.isturn) {
      _this.getAddrList();
    }

    wx.removeStorageSync('ticketList')

  },
  setShuiPiao() {

    var _this = this;
    var chooseDrawBill = this.data.chooseDrawBill;
    var cartItems = wx.getStorageSync('cartItems');
    // if (util.judgeModel('spdk') == 0) { // add by yxw 2020-3-23 水票抵扣先不判断没有该权限
    var sureUseTicket = _this.data.sureUseTicket; // 已选择水票的集合
    // 提前处理选中的水票  将modelid一致的规成一个数组
    var discount = 0; // 优惠总额
    var useTicketNum = 0; // 使用的水票数量
    var r4Data = []; // 结算提交--水票使用
    for (var i in cartItems) {
      for (var j in sureUseTicket) {
        if (cartItems[i].cartShop != null) {
          if (cartItems[i].cartShop.skuId == sureUseTicket[j].productModelId) {
            if (cartItems[i].prouderNum <= sureUseTicket[j].count) { // 如果结算的商品数量 小于等于 水票的数量
              useTicketNum += cartItems[i].prouderNum;
              if (cartItems[i].cartShop.vipPrice != null) {
                discount += cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice;
              } else {
                discount += cartItems[i].prouderNum * cartItems[i].prouderPrice;
              }
              if (Number(Number(sureUseTicket[j].count) - Number(sureUseTicket[j].discountsNum)) >= Number(useTicketNum)) { // 使用总数量 小于 水票购买的数量 全是买的
                r4Data.push({
                  "waterId": sureUseTicket[j].relevancesId,
                  "number": cartItems[i].prouderNum,
                  "waterName": sureUseTicket[j].specification,
                  "waterMoney": sureUseTicket[j].face,
                  "state": 1,
                  "allPrice": Number(cartItems[i].prouderNum) * Number(sureUseTicket[j].face),
                  "deductNum": 0, // 买的水票抵扣的数量
                  "discountsNum": 0, // 赠的水票抵扣的数量
                  "num": cartItems[i].prouderNum, // 买的兑换的数量
                  "discountsNumWater": 0 // 赠的水票兑换的数量
                })
              } else { // 部分买的 部分赠的
                r4Data.push({
                  "waterId": sureUseTicket[j].relevancesId,
                  "number": cartItems[i].prouderNum,
                  "waterName": sureUseTicket[j].specification,
                  "waterMoney": sureUseTicket[j].face,
                  "state": 1,
                  "allPrice": Number(cartItems[i].prouderNum) * Number(sureUseTicket[j].face),
                  "deductNum": 0,
                  "discountsNum": 0,
                  "num": Number(sureUseTicket[j].count) - Number(sureUseTicket[j].discountsNum),
                  "discountsNumWater": Number(useTicketNum) - (Number(sureUseTicket[j].count) - Number(sureUseTicket[j].discountsNum))
                })
              }
              cartItems[i].prouderNum = cartItems[i].prouderNum - cartItems[i].prouderNum
            } else { // 如果结算的商品数量 大于 水票的数量
              //
              useTicketNum += sureUseTicket[j].count;
              if (cartItems[i].cartShop.vipPrice != null) {
                discount += sureUseTicket[j].count * cartItems[i].cartShop.vipPrice;
              } else {
                discount += sureUseTicket[j].count * cartItems[i].prouderPrice;
              }
              r4Data.push({
                "waterId": sureUseTicket[j].relevancesId,
                "number": sureUseTicket[j].count,
                "waterName": sureUseTicket[j].specification,
                "waterMoney": sureUseTicket[j].face,
                "state": 1,
                "allPrice": Number(cartItems[i].prouderNum) * Number(sureUseTicket[j].face),
                "deductNum": 0,
                "discountsNum": 0,
                "num": useTicketNum - Number(sureUseTicket[j].discountsNum),
                "discountsNumWater": Number(sureUseTicket[j].discountsNum)
              })
              cartItems[i].prouderNum = cartItems[i].prouderNum - sureUseTicket[j].count
            }
          }
        }
      }
    }
    // add by yxw 2020-3-13 取消选中水票，支付方式修改
    let payTypeShow = "";
    if (Number(_this.data.shouldPaytotal - discount) > 0 && _this.data.payType == "水票支付") {
      payTypeShow = "微信支付"
    } else if (Number(_this.data.shouldPaytotal - discount) == 0) {
      payTypeShow = "水票支付"
    } else {
      payTypeShow = _this.data.payType
    }
    _this.setData({
      discount: Number(discount).toFixed(2),
      total: Number(_this.data.shouldPaytotal - discount - _this.data.ticketPrice + Number(_this.data.bucketMoneyTotal) +
        (_this.data.isUpPrice ? _this.data.upPrice : 0) +
        ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)
      ) < 0 ? 0 : Number(_this.data.shouldPaytotal - discount - _this.data.ticketPrice + Number(_this.data.bucketMoneyTotal) +
        (_this.data.isUpPrice ? _this.data.upPrice : 0) +
        ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)
      ).toFixed(2),
      orderTotalPrice: Number(Number(_this.data.shouldPaytotal) + Number(_this.data.bucketMoneyTotal)).toFixed(2),
      payType: payTypeShow,
      chooseDrawBill: Number(_this.data.shouldPaytotal - discount) == 0 ? false : chooseDrawBill,
      invoiceMoney: Number(_this.data.shouldPaytotal - discount - _this.data.ticketPrice +
        (_this.data.isUpPrice ? _this.data.upPrice : 0) +
        ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)).toFixed(2),
      showDiscountTotal: Number(Number(_this.data.discountTotal) + Number(discount)).toFixed(2),
      useTicketNum: useTicketNum,
      r4Data: r4Data,
      sureUseTicket: sureUseTicket,
    })
  },
  setShuiPiaoNo() {
    
    var _this = this;
    var chooseDrawBill = this.data.chooseDrawBill;
    var cartItems = wx.getStorageSync('cartItems');
    // if (util.judgeModel('spdk') == 0) { // add by yxw 2020-3-23 水票抵扣先不判断没有该权限
    var sureUseTicket = _this.data.sureUseTicket; // 已选择水票的集合
    // 提前处理选中的水票  将modelid一致的规成一个数组
    var discount = 0; // 优惠总额
    var useTicketNum = 0; // 使用的水票数量
    var r4Data = []; // 结算提交--水票使用
    // add by yxw 2020-3-13 取消选中水票，支付方式修改
    let payTypeShow = "";
    if (Number(_this.data.shouldPaytotal - discount) > 0 && _this.data.payType == "水票支付") {
      payTypeShow = "微信支付"
    } else if (Number(_this.data.shouldPaytotal - discount) == 0) {
      payTypeShow = "水票支付"
    } else {
      payTypeShow = _this.data.payType
    }
    _this.setData({
      discount: Number(discount).toFixed(2),
      total: Number(_this.data.shouldPaytotal - discount - _this.data.ticketPrice + Number(_this.data.bucketMoneyTotal) +
        (_this.data.isUpPrice ? _this.data.upPrice : 0) +
        ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)) < 0 ? 0 : Number(_this.data.shouldPaytotal - discount - _this.data.ticketPrice + Number(_this.data.bucketMoneyTotal) +
        (_this.data.isUpPrice ? _this.data.upPrice : 0) +
        ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)).toFixed(2),
      orderTotalPrice: Number(Number(_this.data.shouldPaytotal) + Number(_this.data.bucketMoneyTotal)).toFixed(2),
      payType: payTypeShow,
      chooseDrawBill: Number(_this.data.shouldPaytotal - discount) == 0 ? false : chooseDrawBill,
      invoiceMoney: Number(_this.data.shouldPaytotal - discount - _this.data.ticketPrice +
        (_this.data.isUpPrice ? _this.data.upPrice : 0) +
        ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)).toFixed(2),
      showDiscountTotal: Number(Number(_this.data.discountTotal) + Number(discount)).toFixed(2),
      useTicketNum: useTicketNum,
      r4Data: r4Data,
      sureUseTicket: sureUseTicket,
    })
  },
  calGoodsPostage: function () {
    var that = this;
    //var _ = that.data.activityGoodsPostage ;
    var _hasActivityGoods = that.data.hasActivityGoods;
    var _addressInfo = that.data.addressInfo;
    //有活动的商品 且不是青浦区
    if (_hasActivityGoods && _addressInfo) {
      var _activityGoodsPostage = 0;
      if (_addressInfo.area !== '青浦区') {
        _activityGoodsPostage = 10 * that.data.totalNum; //邮费
      }

      that.setData({
        activityGoodsPostage: _activityGoodsPostage,
        orderTotalPrice: Number(Number(that.data.orderTotalPrice) + Number(_activityGoodsPostage)).toFixed(2),
        total: Number(Number(that.data.total) + Number(_activityGoodsPostage)) < 0 ? 0 : Number(Number(that.data.total) + Number(_activityGoodsPostage)).toFixed(2)
      }, function () {

      });
    }
  },

  getAddrList() {
    var _this = this;
    // 我的地址 
    user.address({
      'userId': app.userId
    }, function (res) {
      if (res.data.code == 1) {
        if (res.data.data && res.data.data.length > 1) {
          // 超过1个地址
          _this.chooseInfo();
        }
      } else {}
    });
  },
  defaultAddress() {
    var _this = this;
    // 查询默认地址 defaultAddress
    app.request({
      url: config.defaultAddress,
      data: {
        userId: app.userId,
      },
      success(res) {
        //console.log(res);
        if (res.data.code == 1) {
          _this.setData({
            is_info: true,
            addressInfo: res.data.data
          }, function () {
            _this.calGoodsPostage(); //计算邮费 
            // 判断是否在地址附近 
            // _this.checkAddress(res.data.data.addressId);
      if(_this.data.addressInfo) {
            _this.getUpPrice(_this.data.addressInfo.addressId);
      }
          });
        } else {
          _this.setData({
            is_info: false,
            addressInfo: {}
          })
        }
      },
    })
  },
  checkAddress(addressId) {
    // let _this = this;
    // app.request({
    //   url: config.szmcaddresscontrollercheckDistance,
    //   data: {
    //     addressId: addressId,
    //     lat: app.lat,
    //     lon: app.lon
    //   },
    //   success(res) {
    //     if (res.data.code == 1) {
    //       if(res.data.data > 500) {
    //           _this.setData({
    //             showModalAddress: true
    //           })
    //       }
    //     }
    //   }
    // })
  },
  getUpPrice(addressId) {
    let _this = this;
    app.request({
      url: config.szmcaddresscontrollergetUpPrice,
      data: {
        addressId: addressId,
        userId: app.userId,
        storeId: app.storeId
      },
      success(res) {
        if (res.data.code == 1) {
          _this.setData({
            upPrice: res.data.data
          })
          if (_this.data.isUpPrice) {
            _this.setShuiPiao();
          }
        }
      }
    })
  },
  showNextImage(e) {
    let showGuideImage = this.data.showGuideImage;
    this.setData({
      showGuideImage: showGuideImage + 1
    });
  },
  showNextImageLast(e) {
    let _this = this;
    app.request({
      url: config.updatemaskbyid,
      data: {
        maskId: _this.data.szmCMaskId
      },
      success(res) {
        if (res.data.code == 1) {
          _this.setData({
            showMaskGuide: false,
          })
        }
      }
    })
  },
  getcanuseticket() {
    let _this = this;
    app.request({
      url: config.ticketUsercanuse,
      data: {
        storeId: app.storeId,
        userId: app.userId,
      },
      success(res) {
        if (res.data.code == 1) {
          _this.setData({
            canuserticket: res.data.data,
          })
        }
      }
    })
  },
  selectallbyuseridMaskGuide() {
    let _this = this;
    app.request({
      url: config.selectallbyuseridMaskGuide,
      data: {
        userId: app.userId
      },
      success(res) {
        console.log(res);
        if (res.data.code == 1) {
          let list = res.data.data;
          for (var i in list) {
            if (list[i].maskName == 'jsxq' && list[i].maskState == 0) {
              _this.setData({
                showMaskGuide: false, //默认全部关闭
                szmCMaskId: list[i].szmCMaskId
              })
            }
          }
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  // 配送说明 goExplainInfo
  goExplainInfo(e) {
    wx.navigateTo({
      url: '/cart/settlement/explain/explain',
    })
  },
  shopServiceInfo(e) {
    wx.navigateTo({
      url: '/cart/settlement/service/service',
    })
  },
  // 打开发票界面
  goOpenBill(e) {
    let that = this;
    that.setData({
      showModalBillStatus: true
    })
  },
  // 选择开票信息
  invoiceInfoDataFun(info) {
    this.hideModal();
    if (!info.detail.onlyClose) {
      console.log(info.detail.invoiceInfo);
      this.setData({
        invoiceOrder: info.detail.invoiceInfo
      });
    }
  },

  changeBucketStatus(e) {
    let index = e.currentTarget.dataset.index,
      productId = e.currentTarget.dataset.productid,
      source = e.currentTarget.dataset.source,
      isBuckets = e.detail.value,
      list = this.data.cartItems;
    if (isBuckets) {
      this.selectBucketbycardId(productId, source, index);
    } else {
      list[index].isBuckets = isBuckets;
      this.setData({
        cartItems: list
      }, () => {
        // 计算押桶金总数
        this.calculationBucketMoney();
      })
    }
  },

  // 查询购物车中桶装水押桶信息selectbucketbycardid
  selectBucketbycardId(productId, source, index) {
    let _this = this,
      list = this.data.cartItems;
    util.ajax(config.selectbucketbycardid, {
      productId: productId,
      source: source,
      userId: app.userId,
      storeId: app.storeId
    }, res => {
      if (res.data.code == 1) {
        let bucketNumList = [],
          newListNum = [],
          newListData = [],
          dataList = res.data.data;
        if (source == 0) {
          dataList[0].maxNum = list[index].prouderNum;
          dataList[0].num = list[index].prouderNum;
          dataList[0].brandName = dataList[0].name;
          // 添加一个商品skuID
          dataList[0].skuId = list[index].cartShop.skuId;
        } else {
          list[index].cartShopGroupList.list.forEach(x => {
            // add 2020-3-10 by yxw 一次性桶不需要押桶
            if (x.className == '桶装水' && x.buckType != 1) {
              bucketNumList.push({
                num: Number(x.skuNumber) * Number(list[index].prouderNum),
                brandId: x.brandId,
                skuId: x.skuId
              })
            }
          })
          newListNum = this.mergeList(bucketNumList, 'add'); // 套餐商品使用，去除重复的项 押桶数量相加
          newListData = this.mergeList(dataList, 'noAdd'); // 套餐商品使用，去除重复的项 押桶数量不相加
          newListData.forEach((x, indexDetail) => {
            x.maxNum = newListNum[indexDetail].num
            x.num = newListNum[indexDetail].num
            x.brandName = x.name
            x.skuId = newListNum[indexDetail].skuId // add 2020-2-22 添加skuId
          })
        }
        list[index].isBuckets = true;
        list[index].bucketsList = source == 0 ? dataList : newListData;
      } else {
        list[index].isBuckets = false;
        list[index].bucketsList = [];
        util.showText('请联系商户设置桶押金');
      }
      _this.setData({
        cartItems: list
      }, () => {
        // 计算押桶金总数
        this.calculationBucketMoney();
      })
    });
  },

  // 合并数组中重复的对象
  mergeList(list, type) {
    let map = {},
      dest = [];
    for (let i = 0; i < list.length; i++) {
      let ai = list[i];
      if (type == 'add') {
        if (!map[ai.brandId]) {
          dest.push({
            brandId: ai.brandId,
            num: ai.num,
            skuId: ai.skuId // 添加skuId
          });
          map[ai.brandId] = ai;
        } else {
          for (var j = 0; j < list.length; j++) {
            let dj = dest[j];
            if (dj.brandId == ai.brandId) {
              dj.num = (Number(dj.num) + Number(ai.num));
              break;
            }
          }
        }
      } else {
        if (!map[ai.brandId]) {
          dest.push(ai);
          map[ai.brandId] = ai;
        }
      }
    }
    return dest;
  },

  calculationBucketMoney() {
    let list = this.data.cartItems,
      money = 0,
      returnBackBucketList = [];
    //bucketsList
    list.forEach(x => {
      if (x.isBuckets && x.bucketsList) {
        x.bucketsList.forEach(y => {
          money += Number(y.num) * Number(y.price);
          returnBackBucketList.push(y);
        })
      }
    })
    var _this = this;
    this.setData({
      bucketMoneyTotal: money.toFixed(2),
      // 加上押桶金
      total: Number(_this.data.shouldPaytotal - _this.data.discount - _this.data.ticketPrice + Number(money) +
      (_this.data.isUpPrice ? _this.data.upPrice : 0) +
      ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)) < 0 ? 0 : Number(_this.data.shouldPaytotal - _this.data.discount - _this.data.ticketPrice + Number(money) +
      (_this.data.isUpPrice ? _this.data.upPrice : 0) +
      ((_this.data.store.minnumber > _this.data.totalNum && _this.data.store.minprice > 0) ? _this.data.store.minprice : 0)).toFixed(2),
    orderTotalPrice: Number(Number(_this.data.shouldPaytotal) + Number(money)).toFixed(2),
      returnBackBucketList: returnBackBucketList // 押桶类型的集合
    })
  },

  changeBucketNum(e) {
    let index = e.currentTarget.dataset.index,
      bucketIndex = e.currentTarget.dataset.bucketindex,
      value = e.detail.value,
      list = this.data.cartItems;
    if (Number(value) > Number(list[index].bucketsList[bucketIndex].maxNum)) {
      util.showText('押桶数量不能超过购买数量');
      list[index].bucketsList[bucketIndex].num = list[index].bucketsList[bucketIndex].maxNum;
      // } else if(value==0) {
      //   util.showText('押桶数量必须大于0');
      //   list[index].bucketsList[bucketIndex].num = 1;
    } else {
      list[index].bucketsList[bucketIndex].num = value;
    }
    this.setData({
      cartItems: list
    }, () => {
      // 计算押桶金总数
      this.calculationBucketMoney();
    })
  },

  reduceBucketNum(e) {
    let index = e.currentTarget.dataset.index,
      bucketIndex = e.currentTarget.dataset.bucketindex,
      list = this.data.cartItems,
      value = list[index].bucketsList[bucketIndex].num;
    if (Number(value) == 1) {
      util.showText('亲，不能再少了');
      list[index].bucketsList[bucketIndex].num = 1;
    } else {
      list[index].bucketsList[bucketIndex].num = --value;
    }
    this.setData({
      cartItems: list
    }, () => {
      // 计算押桶金总数
      this.calculationBucketMoney();
    })
  },

  addBucketNum(e) {
    let index = e.currentTarget.dataset.index,
      bucketIndex = e.currentTarget.dataset.bucketindex,
      list = this.data.cartItems,
      value = list[index].bucketsList[bucketIndex].num;
    ++value;
    if (Number(value) > Number(list[index].bucketsList[bucketIndex].maxNum)) {
      util.showText('押桶数量不能超过购买数量');
      list[index].bucketsList[bucketIndex].num = list[index].bucketsList[bucketIndex].maxNum;
    } else {
      list[index].bucketsList[bucketIndex].num = value;
    }
    this.setData({
      cartItems: list
    }, () => {
      // 计算押桶金总数
      this.calculationBucketMoney();
    })
  },

  changeDrawBill(e) {
    if (this.data.invoiceMoney == '0.00') {
      this.setData({
        chooseDrawBill: false,
      })
      return util.showText('开票金额0，不可开发票');
    } else {
      this.setData({
        chooseDrawBill: e.detail.value,
        invoiceOrder: {
          invoiceType: 0,
          riseType: 0,
          acceptType: 0
        }
      })
    }
  },
  payTypeSelectFun(e) {
    this.setData({
      payType: e.detail.payType,
      checkboxselect: 0,
    })
    this.setShuiPiaoNo();
  },
  bindchangeticket(e) {
    var that = this;
    let check = e.detail.useTicketNum;
    if (check) {
      // 使用水票
      this.setShuiPiao();
    } else {
      // 不使用水票
      this.setShuiPiaoNo();
    }
  },

  homeStore() {
    let that = this,
      params = {
        storeId: app.storeId,
        lat: app.lat,
        lon: app.lon
      };
    util.ajax(config.homeStore, params, res => {
      if (res.data.code == 1) {
        let shopInfoData = res.data.data
        wx.setStorageSync('shopInfo', shopInfoData);
        that.setData({
          store: res.data.data.szmCStore,
          yunfei: (res.data.data.szmCStore.minnumber > that.data.totalNum && res.data.data.szmCStore.minprice > 0) ?
            res.data.data.szmCStore.minprice : 0
        })
        if (!util.judgeTimeInOpen(shopInfoData.szmCStore.storeServicetime.split("-")[0], shopInfoData.szmCStore.storeServicetime.split("-")[1])) {
          that.setData({
            showCloseTip: true
          })
        }
      }
    });
  },
  mergeListbbb(list) {
    let map = {},
      dest = [];
    for (let i = 0; i < list.length; i++) {
      let ai = list[i];
      let string = ai.waterCoupon + ai.price;
      if (!map[string]) {
        dest.push(ai);
        map[string] = ai;
      } else {
        for (var j = 0; j < list.length; j++) {
          let dj = dest[j];
          if ((dj && dj.relevancesId == ai.relevancesId) && (dj && dj.price == ai.price)) {
            // if ((dj.waterCoupon == ai.waterCoupon) && (dj.price == ai.price)) {
            dj.count = (Number(dj.count) + Number(ai.count));
            break;
          }
        }
      }
    }
    return dest;
  },
  goSelectTicket() {
    wx.navigateTo({
      url: '/pages/user/ticketSelect/ticketSelect',
    })
  },
  addressConfirm() {
    this.setData({
      showModalAddress: false
    })
  },
  changeIsUpPrice() {
    this.setData({
      isUpPrice: this.data.isUpPrice ? 0 : 1
    })
    this.setShuiPiao();
  },
  goAddGood() {
    wx.navigateTo({
      url: '/user/classify/classify',
    })
  },
  getdrainageuser() {
    
    util.ajax(config.drainageUsergetInfoById, {
      drainageUserId: this.data.drainageuserid,
    }, res => {
      if (res.data.code == 1) {
        this.setData({
          drainageUser: res.data.data.drainageUser,
          drainage: res.data.data.drainage,
          drainageid: res.data.data.drainage.id,
        });
      }
    })
  },
  
  add: function (e) {
    var cartItems = this.data.cartItems //获取购物车列表
    var index = e.currentTarget.dataset.index //获取当前点击事件的下标索引
    var id = e.currentTarget.dataset.id //获取当前点击事件的下标索引
    var source = e.currentTarget.dataset.source //source // source
    var value = cartItems[index].prouderNum //获取购物车里面的value值
    value++
    cartItems[index].prouderNum = value;
    this.setData({
      cartItems: cartItems
    });
    var that = this;
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    wx.request({
      url: config.addOrSubNum,
      data: {
        cartMainId: id,
        productNum: value,
        r1: source
      },
      method: 'POST',
      success(res) {
        wx.setStorageSync("cartItems", cartItems);
        that.dualPrice();
        wx.hideLoading();
      },
      fail() {}
    })
  },
  //减
  reduce: function (e) {
    var that = this;
    var cartItems = this.data.cartItems //获取购物车列表
    var index = e.currentTarget.dataset.index //获取当前点击事件的下标索引
    var id = e.currentTarget.dataset.id //id
    var source = e.currentTarget.dataset.source //source // source
    var value = cartItems[index].prouderNum //获取购物车里面的value值
    if (value == 1) {
      value--
      cartItems[index].prouderNum = 1;
      wx.showToast({
        title: '亲，不能再少了',
        icon: 'none'
      })
      return;
    } else {
      value--
      cartItems[index].prouderNum = value;
    }
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    wx.request({
      url: config.addOrSubNum,
      data: {
        cartMainId: id,
        productNum: value,
        r1: source
      },
      method: 'POST',
      success(res) {
        wx.setStorageSync("cartItems", cartItems);
        that.dualPrice();
        wx.hideLoading();
      },
      fail() {}
    })
  },
  dualPrice() {
    var cartItems = wx.getStorageSync('cartItems'),
      login = wx.getStorageSync('login'),
      shopInfo = wx.getStorageSync('shopInfo'),
      goodsTotal = 0, // 普通商品总价
      goodsTotalOriginal = 0, // 普通商品原价
      groomsTotal = 0, //套餐数据总价
      groomsTotalOriginal = 0, //套餐数据总价
      smzCGroupOrderList = [], //套餐数据
      smzCOrderDetailsList = [], // 普通商品
      cartIds = [], // 购物车 id total
      totalNum = 0, //结算总数量
      isOnlyGroup = 1,
      isOnlyDiscouts = 1,
      userPayOnUpPrice = 0, // 客户付上楼费，计算方式修改
      totalSingleGroupNum = 0; // 商品数量，包括套餐中商品的数量
    var _actGoods = false;
    for (var i in cartItems) {
      if (cartItems[i].selected) {
        cartIds.push(cartItems[i].cartId);
        if (!_actGoods && cartItems[i].source === '5') {
          _actGoods = true;
        }
        if (cartItems[i].cartShop == null && cartItems[i].cartShopGroupList.skuNumber != '商品数量不足' && cartItems[i].cartShopGroupList.groupState == 0) {
          isOnlyDiscouts = 0;
          smzCGroupOrderList.push({
            groupOrderProductPrice: cartItems[i].cartShopGroupList.rulingPrice * cartItems[i].prouderNum,
            orderProductNum: cartItems[i].prouderNum,
            shopgroupId: cartItems[i].cartShopGroupList.groupId
          })
          groomsTotal += cartItems[i].cartShopGroupList.rulingPrice * cartItems[i].prouderNum;
          groomsTotalOriginal += cartItems[i].cartShopGroupList.originalPrice * cartItems[i].prouderNum;
          totalNum += cartItems[i].prouderNum;
          // 判断是否是桶装水
          cartItems[i].isBarreledWater = false
          cartItems[i].isBuckets = false
          cartItems[i].cartShopGroupList.list.forEach(x => {
            if (x.className == "桶装水" && x.buckType != 1) {
              cartItems[i].isBarreledWater = true
            }
            totalSingleGroupNum += (cartItems[i].prouderNum * x.skuNumber);
          });
        } else if (cartItems[i].cartShopGroupList == null && cartItems[i].cartShop.skuNumber != '商品数量不足' && cartItems[i].cartShop.skuState == 0) {
          isOnlyGroup = 0;
          if (cartItems[i].cartShop.marketPrice != null) {
            if (cartItems[i].cartShop.vipPrice != null) {
              isOnlyDiscouts = 1;
              // 0普通1限时2买赠3优惠
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].cartShop.marketPrice;
            } else {
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].prouderPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].cartShop.marketPrice;
            }
          } else {
            if (cartItems[i].cartShop.vipPrice != null) {
              isOnlyDiscouts = 1;
              // 0普通1限时2买赠3优惠
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].cartShop.vipPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
            } else {
              smzCOrderDetailsList.push({
                orderDetailsProductPrice: cartItems[i].prouderNum * cartItems[i].prouderPrice,
                orderProductNum: cartItems[i].prouderNum,
                productModelId: cartItems[i].cartShop.skuId,
                productSkuimg: cartItems[i].cartShop.skuPicture,
                productSkuname: cartItems[i].cartShop.skuName,
                marketPrice: cartItems[i].cartShop.marketPrice,
                r2: cartItems[i].source,
                source: cartItems[i].cartShop.newSource,
              })
              goodsTotal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
              goodsTotalOriginal += cartItems[i].prouderNum * cartItems[i].prouderPrice;
            }
          }
          totalNum += cartItems[i].prouderNum;
          totalSingleGroupNum += cartItems[i].prouderNum;
          // 判断是否是桶装水
          if (cartItems[i].cartShop.className == "桶装水" && cartItems[i].cartShop.buckType != 1) {
            cartItems[i].isBarreledWater = true
          } else {
            cartItems[i].isBarreledWater = false
          }
          cartItems[i].isBuckets = false
        }

      }
    }
    userPayOnUpPrice = Number(Number(login[0].upFloorMoney) * Number(totalSingleGroupNum)).toFixed(2);
    if (shopInfo != null || shopInfo != '' || shopInfo != undefined) {
      this.setData({
        // freightAndUpfloor: Number(Number(shopInfo.szmCStore.storeShippingfee) + Number(login[0].upFloorMoney)).toFixed(2), //修改
        freightAndUpfloor: Number(userPayOnUpPrice).toFixed(2) // 客户付上楼费单价
      })
    }
    this.setData({
      isOnlyGroup: isOnlyGroup, // 是否只有套餐
      isOnlyDiscouts: isOnlyDiscouts, // 是否只有优惠
      cartItems: cartItems,
      goodsTotal: Number(goodsTotal).toFixed(2),
      goodsTotalOriginal: Number(goodsTotalOriginal).toFixed(2),
      goodsTotalDiscounts: Number(Number(goodsTotalOriginal) - Number(goodsTotal)).toFixed(2),
      groomsTotal: Number(groomsTotal).toFixed(2),
      groomsTotalOriginal: Number(groomsTotalOriginal).toFixed(2),
      groomsTotalDiscounts: Number(Number(groomsTotalOriginal) - Number(groomsTotal)).toFixed(2),
      cartIds: cartIds,
      totalNum: totalSingleGroupNum,
      hasActivityGoods: _actGoods, //是否活动商品
      total: (Number(goodsTotal) + Number(groomsTotal) + Number(shopInfo.szmCStore.storeShippingfee) + Number(userPayOnUpPrice)) < 0 ? 0 : (Number(goodsTotal) + Number(groomsTotal) + Number(shopInfo.szmCStore.storeShippingfee) + Number(userPayOnUpPrice)),
      balance: login[0].money,
      random: Math.ceil(Math.random() * 100000),
      smzCGroupOrderList: smzCGroupOrderList,
      smzCOrderDetailsList: smzCOrderDetailsList,
      //结算商品原总价
      totalOriginal: Number(Number(goodsTotalOriginal) + Number(groomsTotalOriginal)).toFixed(2),
      //结算商品应付总价
      shouldPaytotal: Number(Number(goodsTotal) + Number(groomsTotal) + Number(shopInfo.szmCStore.storeShippingfee) + Number(userPayOnUpPrice)).toFixed(2),
      // 优惠合计
      discountTotal: Number(Number(goodsTotalOriginal) + Number(groomsTotalOriginal) - Number(goodsTotal) - Number(groomsTotal)).toFixed(2),
      showDiscountTotal: Number(Number(goodsTotalOriginal) + Number(groomsTotalOriginal) - Number(goodsTotal) - Number(groomsTotal)).toFixed(2),
    })
    if (this.data.payType == '水票支付') {
      // 使用水票
      this.setShuiPiao();
    } else {
      // 不使用水票
      this.setShuiPiaoNo();
    }
  }
})