/* components/payTypeSelect/payTypeSelect.wxss */
@import "../../app.wxss";

.pay-item{
	display: flex;
	justify-content: space-between;
	justify-items: center;
	flex-wrap: nowrap;
	align-items:center;
	align-content: center;
	height: 80rpx;
}

.pay-item-left{
	width: 70%;
	padding-left: 8rpx;
	height: 100%;
	
	display: flex;
	justify-content: flex-start;
	justify-items: center;
	align-items:center;
	align-content: center;
}

.paly-item-text{
	padding-left: 16rpx;
}


.pay-item-right{
	width: 50%;
	display: flex;
	justify-content:flex-end  ;
}
/* 未选中的背景样式 */
checkbox .wx-checkbox-input{
  width: 40rpx; 
  height: 40rpx; 
  border-radius: 50%;
}

/* 选中后的背景样式 */
checkbox .wx-checkbox-input.wx-checkbox-input-checked{
  border: none;
  background: #37C674;
}

/* 选中后的勾子样式 */
checkbox .wx-checkbox-input.wx-checkbox-input-checked::before{
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  border-radius: 50%;
  text-align: center;
  font-size:32rpx; 
  color:#FFF; 
  background: transparent;
  transform:translate(-50%, -50%) scale(1);
  -webkit-transform:translate(-50%, -50%) scale(1);
}

