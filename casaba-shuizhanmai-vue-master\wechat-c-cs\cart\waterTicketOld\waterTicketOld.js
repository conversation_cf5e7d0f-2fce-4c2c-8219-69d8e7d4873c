import config from '../../config.js';
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imgUri: app.imgUri,
    cartItems: [],
    cantCartItems: [], // 不能使用的水票
    originalCartItems: [], // 原水票数组
    is_select: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log(options);
    var _this = this;

    wx.request({
      url: config.isHaveTicket,
      data: {
        productModelId: JSON.parse(options.idArr),
        userId: app.userId,
        storeId: app.storeId,
      },
      method: "POST",
      success(res) {
        if (res.data.code == 1) {
          console.log(JSON.stringify(res.data.data));
          let list = JSON.parse(JSON.stringify(res.data.data)),
            cartItems = [],
            cantCartItems = [];
          list.forEach(x => {
            if (x.isUsable == 1) {
              cartItems.push(x)
            } else {
              cantCartItems.push(x)
            }
          })

          // 合并水票记录中相同的水票
          let newCartItems = _this.mergeList(cartItems);
          let newCantCartItems = _this.mergeList(cantCartItems);

          _this.setData({
            cartItems: newCartItems,
            cantCartItems: newCantCartItems,
            originalCartItems: res.data.data
          })
          let is_select = [];
          for (let i = 0; i < newCartItems.length; i++) {
            let arr = {
              'selected': true
            };
            is_select.push(arr);
          }
          _this.setData({
            is_select: is_select
          })
        } else {
          _this.setData({
            cartItems: '',
          })
        }
      },
    })

  },
  // 确定使用 sureUseTicket
  sureUseTicket(e) {

    let _this = this,
      is_select = _this.data.is_select,
      originalCartItems = _this.data.originalCartItems, // 原始水票list
      cartItems = _this.data.cartItems, // 合并水票list
      data = [];
    for (var i in is_select) {
      if (is_select[i].selected) {
        originalCartItems.forEach(x => {
          // 从合并集合中选取出对应的原始水票list中的对象
          if ((x.waterCoupon == cartItems[i].waterCoupon) && (x.price == cartItems[i].price)) {
            data.push(x);
          }
        })
      }
    }
    let pages = getCurrentPages();
    let prevPage = pages[pages.length - 2];
    prevPage.setData({
      sureUseTicket: data,
    })
    wx.navigateBack({
      delta: 1,
    })
  },
  // 选择
  selectedCart: function(e) {
    var cartItems = this.data.is_select; //获取购物车列表
    var index = e.currentTarget.dataset.index; //获取当前点击事件的下标索引
    var selected = cartItems[index].selected; //获取购物车里面的value值
    var _this = this;
    //取反
    cartItems[index].selected = !selected;
    _this.setData({
      is_select: cartItems
    })
  },

  mergeList(list) {
    let map = {},
      dest = [];
    for (let i = 0; i < list.length; i++) {
      let ai = list[i];
      let string = ai.waterCoupon + ai.price;
      if (!map[string]) {
        dest.push(ai);
        map[string] = ai;
      } else {
        for (var j = 0; j < list.length; j++) {
          let dj = dest[j];
          if ((dj.waterCoupon == ai.waterCoupon) && (dj.price == ai.price)) {
            dj.count = (Number(dj.count) + Number(ai.count));
            break;
          }
        }
      }
    }
    return dest;
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})