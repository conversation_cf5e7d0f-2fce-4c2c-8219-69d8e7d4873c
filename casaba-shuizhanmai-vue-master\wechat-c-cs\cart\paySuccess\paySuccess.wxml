<!-- 结算成功  支付成功页面 -->
<import src="../../pages/template/goods-item/groom/groom.wxml" />
<view class='page'>
  <view class='pay-title' style='background:url("{{imgUri}}/images/index/yq-bg-1.png") no-repeat ; background-size: 100% ;'>
    <view class='cont' style="{{showCloseTip?'':''}}">
      <image class='people' src='{{imgUri}}/images/people.png'></image>
      <view wx:if="{{showCloseTip}}" class="color-red margin-bottom-10">本店24小时可下单。现在店铺休息中，营业后将优先为您配送!</view>
      <view class="color-red margin-bottom-10">店铺营业时间：{{storeServicetime}}</view>
      <view>支付成功，感谢您对我们的信任与支持， 我们将尽快为您配送~</view>
      <view style="color: green;font-weight: 500;" wx:if="{{dikou != 1 && integralState && distribution > 0 && total != '0.00'}}">恭喜您获得返现抵扣金<text style="font-weight: 600;font-size: 34rpx;color: red;">{{total * distribution}}元</text>可在返现抵扣金换购区使用。订单签收后自动到账。在我的里面查看，谢谢。</view>
      <view class='df'>
        <view class='button' bindtap='goTarget' data-type='1'>返回首页</view>
        <view class='button' bindtap='goTarget' data-type='2'>返回个人中心</view>
      </view>
    </view>
  </view>

  <view wx:if="{{integralState && distribution > 0 && total != '0.00'}}">
<view style="height: 88rpx;line-height: 88rpx;text-align: center;font-weight: 600;">返现抵扣金商品推荐</view>
<view class='content groom content_shop_list' wx:if="{{groom.length > 0 }}">
  <view style="box-sizing: border-box;   display: flex;align-items: flex-start;">
    <view class="container-left" id="left">
      <view class='item-index' wx:for="{{groomleft}}" wx:key="key">
        <view class='marker' wx:if="{{item.location=='左上'}}">{{item.lableName}}</view>
        <view class='item-text'>
          <view data-id="{{item.productModelId}}" bindtap='goGoodsInfo'>
            <view class='item-img' style="position:relative;">
              <image src='{{item.img}}'></image>
            </view>
            <view class='title'>{{item.productTitle}}</view>
            <!-- 规格 -->
            <view class="item-box-index-tip" wx:if="{{item.specificationsDescribe}}">
              <text class="one-use-bucket">{{item.specificationsDescribe}}</text>
            </view>
            <view style="display: flex;flex-wrap: wrap;">
              <view class='dikou123' style="margin-left: 20rpx;">支持自提</view>
              <view class='dikou1234' style="margin-left: 20rpx;">支持配送</view>
              <view class='dikou123123' style="margin-left: 20rpx;">每月限购{{item.monthlimit}}件</view>
            </view>
          </view>
          <view class="item-box-index-tip" style="visibility:{{item.location=='左下'||item.buckType==1?'':'hidden'}};">
            <text wx:if="{{item.location=='左下'}}" class="item-box-index-tip-text margin-right-20">{{item.lableName}}</text>
            <text wx:if="{{item.buckType==1}}" class="one-use-bucket">一次性桶</text>
          </view>
          <view class='info df'>
            <!-- 价钱 sbwy begin -->
            <view>
              <view class='old-pricebbb'>零售价：￥{{item.productOriginalPrice}}</view>
              <view class='dikou'>返现抵扣金 {{item.dikou}}</view>
              <view class='realprice'>现付：￥{{item.productOriginalPrice-item.dikou}}元</view>
            </view>
          </view>
        </view>
        <view class="good_box" hidden="{{groomSmall[index].hide_good_box}}" style="left: {{bus_x}}px; top: {{bus_y}}px;">
          <image src="{{item.img}}"></image>
        </view>
        <view class='dowm' catchtap='sureAdd' data-id="{{item.productModelId}}" data-price="{{item.productOriginalPrice}}" data-dikou="{{item.dikou}}" data-peisongfei="{{item.peisongfei}}">点击购买</view>
      </view>
    </view>
    <view class="container-right" id="right" style="margin-left: 20rpx;">
      <view class='item-index' wx:for="{{groomright}}" wx:key="key">
        <view class='marker' wx:if="{{item.location=='左上'}}">{{item.lableName}}</view>
        <view class='item-text'>
          <view data-id="{{item.productModelId}}" bindtap='goGoodsInfo'>
            <view class='item-img' style="position:relative;">
              <image src='{{item.img}}'></image>
            </view>
            <view class='title'>{{item.productTitle}}</view>
            <!-- 规格 -->
            <view class="item-box-index-tip" wx:if="{{item.specificationsDescribe}}">
              <text class="one-use-bucket">{{item.specificationsDescribe}}</text>
            </view>
            <view style="display: flex;flex-wrap: wrap;">
              <view class='dikou123' style="margin-left: 20rpx;">支持自提</view>
              <view class='dikou1234' style="margin-left: 20rpx;">支持配送</view>
              <view class='dikou123123' style="margin-left: 20rpx;">每月限购{{item.monthlimit}}件</view>
            </view>
          </view>
          <view class="item-box-index-tip" style="visibility:{{item.location=='左下'||item.buckType==1?'':'hidden'}};">
            <text wx:if="{{item.location=='左下'}}" class="item-box-index-tip-text margin-right-20">{{item.lableName}}</text>
            <text wx:if="{{item.buckType==1}}" class="one-use-bucket">一次性桶</text>
          </view>
          <view class='info df'>
            <!-- 价钱 sbwy begin -->
            <view>
              <view class='old-pricebbb'>零售价：￥{{item.productOriginalPrice}}</view>
              <view class='dikou'>返现抵扣金 {{item.dikou}}</view>
              <view class='realprice'>现付：￥{{item.productOriginalPrice-item.dikou}}元</view>
            </view>
            <!-- 价钱 sbwy begin -->
            <!-- <view class='df' data-index="{{index}}" data-id="{{item.productModelId}}" data-price="{{item.productOriginalPrice}}" bindtap="touchOnGoods" style='width:100rpx;height:50rpx;text-align:right;justify-content:flex-end;'>
            <image data-id="{{item.id}}" src='{{imgUri}}/images/cart.png'></image>
          </view> -->
          </view>
        </view>
        <view class="good_box" hidden="{{groomSmall[index].hide_good_box}}" style="left: {{bus_x}}px; top: {{bus_y}}px;">
          <image src="{{item.img}}"></image>
        </view>
        <view class='dowm' catchtap='sureAdd' data-id="{{item.productModelId}}" data-price="{{item.productOriginalPrice}}" data-dikou="{{item.dikou}}" data-peisongfei="{{item.peisongfei}}">点击购买</view>
      </view>
    </view>
  </view>
</view>
</view>
  <!-- 推荐商品 groom -->
  <!-- <template is="groom" data="{{number,groom,moreGroom,imgUri,groomBackground,groomTitle,needAni,hideCount,hide_good_box,bus_x,bus_y,count,groomSmall,isShowCar}}"></template> -->
</view>