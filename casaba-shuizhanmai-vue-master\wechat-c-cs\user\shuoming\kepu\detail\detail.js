// user/shuoming/kepu/detail/detail.js
import config from '../../../../config.js';
import util from '../../../../utils/util.js';
var WxParse = require('../wxParse/wxParse.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id:'',
    kepu: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: options.id
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getkepu();
  },

  getkepu: function () {
    var that = this;
    util.ajax(config.kepuread, {
      'id': this.data.id
    }, function (res) {
      if (res.data.code == 1) {
        that.setData({
          kepu: res.data.data || {}
        })
        WxParse.wxParse('topicDetail', 'html', res.data.data.content, that);
      }
    });
    util.ajax(config.kepuaddView, {
      'id': this.data.id
    }, function (res) {
      if (res.data.code == 1) {
      }
    });
  },

  // Go back to previous page
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})