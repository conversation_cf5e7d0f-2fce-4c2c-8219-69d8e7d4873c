<!--cart/index/index.wxml  结算页面-->
<import src="../../pages/template/goods-item/meal/meal.wxml" />
<import src="../../pages/template/loading/loading.wxml" />
<template is="loading" wx:if="{{is_loading}}"></template>

<view class='page' wx:if="{{!(showMaskGuide&&isCanTarget)}}">
  <scroll-view style="height:calc(100vh - 150rpx)" scroll-y>
    <view class='con'>
      <!-- 收货信息 -->
      <view class='item1 df' style='width:100%;box-sizing: border-box' catchtap='chooseInfo'>
        <view class='left' style='width:88%;' wx:if="{{addressInfo}}">
          <view class="flex align-items-center">
            <text wx:if="{{addressInfo.r1!=null}}" class="font-size-22 customer-type">{{addressInfo.r1 == 1 ? '企业':addressInfo.r1 == 0?'家庭':''}}</text>
            <text class="font-size-26">{{addressInfo.province}}{{addressInfo.city}}{{addressInfo.area}}</text>
          </view>
          <view class="font-size-30 bold margin-top-10">{{addressInfo.street}} {{addressInfo.r2 == 1 ? '有电梯·':addressInfo.r2 == 0?'无电梯·':''}}{{addressInfo.r3 == 8 ?'7楼以上': addressInfo.r3!=null?(addressInfo.r3+'楼'):''}} {{addressInfo.r4 != null ?addressInfo.r4 :''}}
          </view>
          <view class='font-size-26 margin-top-10'>
            <text class='name'>{{addressInfo.userName}}</text>
            <text class='phone'>{{addressInfo.telphoneOne}}</text>
          </view>
        </view>


        <view class='left' style='width:88%;' wx:else bindtap='chooseInfo'>
          <view class='info'>首次下单请填写收货地址</view>
        </view>

        <view class='' style='flex:1;text-align:right;padding-right:20rpx;'>
          <image src='{{imgUri}}/images/shop/r-black-icon.png' mode="widthFix" style="width:16rpx"></image>
        </view>
      </view>

      <view class="address-desc">提示:切换家庭或企业地址</view>

      <view class="message-type" style="background-image:url({{imgUri}}/images/shop/message-cut.png)">
      </view>

      <!-- 商品列表 -->
      <view class='goods-item meal padding-top-20'>
        <view wx:for="{{cartItems}}" wx:key="skuPicture" wx:if="{{item.selected}}">
          <view class="cart-box cart_main margin-bottom-20">
            <view data-id="{{item.cartId}}" class='df meal' style='transition: all 0.5s; {{list[index].Style}}' data-index="{{index}}">
              <!-- 普通商品 -->
              <view class='df width-full position-relative' wx:if="{{item.cartShop != null && item.cartShop.skuNumber != '商品数量不足' && item.cartShop.skuState == 0}}" style="padding: 20rpx 35rpx;">
                <view class='marker-new' wx:if="{{item.showDown==1}}" style='display:block;'>已下架</view>
                <view class='left df'>
                  <view class='left_imgae df'>
                    <image class="cart-image" src="{{item.cartShop.skuPicture}}"></image>
                  </view>

                </view>
                <view class='right df' style='flex:1'>
                  <view class='left_box flex flex-direction-column justify-content-between' style="flex-wrap: wrap;flex-direction: row;">
                    <text class="cart-title">{{item.cartShop.skuName}}</text>
                    <text class='cart-rule'>{{item.cartShop.skuValue}}</text>
                    <view class='cart-right flex '>
                      <view wx:if='{{item.cartShop.marketPrice != null && item.cartShop.marketPrice > 0}}'  style="margin-right: 20rpx;">
                        <view class='flex align-items-end' wx:if='{{item.cartShop.vipPrice != null}}'>
                          <text class="cart-price"><text class='money'>￥</text>
                            <text>{{item.cartShop.vipPrice}}</text></text>
                          <text class="old-price"><text class='money'>￥</text>
                            <text>{{item.cartShop.marketPrice}}</text></text>
                        </view>
                        <view class='flex align-items-end' wx:else>
                          <text class="cart-price"><text class='money'>￥</text>
                            <text>{{item.prouderPrice}}</text></text>
                          <text class="old-price"><text class='money'>￥</text>
                            <text>{{item.cartShop.marketPrice}}</text></text>
                        </view>
                      </view>
                      <view wx:else  style="margin-right: 20rpx;">
                        <view class='flex align-items-end' wx:if='{{item.cartShop.vipPrice != null}}'>
                          <text class="cart-price"><text class='money'>￥</text>
                            <text>{{item.cartShop.vipPrice}}</text></text>
                          <text class="old-price" wx:if="{{item.prouderPrice != null && item.prouderPrice > 0}}"><text class='money'>￥</text>
                            <text>{{item.prouderPrice}}</text></text>
                        </view>
                        <view class='flex align-items-end' wx:else>
                          <text class="cart-price"><text class='money'>￥</text>
                            <text>{{item.prouderPrice}}</text></text>
                        </view>
                      </view>
                      <view catchtap='reduce' class="input cart-reduce" style="margin-right: 20rpx;" data-id="{{item.cartId}}" data-source="{{item.source}}" data-index="{{index}}">
                        <image src='/images/sub.png'></image>
                      </view>
                      <view class='flex align-items-end'  style="margin-right: 20rpx;">
                        <view class="font-size-26">x{{item.prouderNum}}</view>
                      </view>
                      <view catchtap='add' class="input cart-add"  data-id="{{item.cartId}}" data-source="{{item.source}}" data-index="{{index}}">
                        <image src='/images/add.png'></image>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <!-- 套餐 -->
              <view class='item item-re box-sizing' wx:if="{{item.cartShop == null }}">
                <view class='marker' style='display:block;' wx:if="{{item.cartShopGroupList.groupType}}">{{item.cartShopGroupList.groupType}}</view>
                <view class='marker 2 under' wx:if="{{item.showDown==1}}" style='display:block;'>已下架</view>
                <view class='df'>
                  <view class='item-img'>
                    <image src='{{item.cartShopGroupList.skuPicture}}'></image>
                  </view>
                  <view class='item-text' style="width:100%">
                    <view class='title' style="font-weight:500">{{item.cartShopGroupList.groupName}}</view>
                    <view class='info df'>
                      <view class='price df'>
                        <text class='margin-right-10 font-size-34 bold color-red'><text class='money'>￥</text>{{item.cartShopGroupList.rulingPrice}}</text>
                        <text class='old-price'><text class='money'>￥</text>{{item.cartShopGroupList.originalPrice}}</text>
                      </view>
                      <view class='flex align-items-end'>
                        <view class="font-size-26">x{{item.prouderNum}}</view>
                      </view>
                    </view>
                  </view>
                </view>
                <view class='flex justify-content-between color-grey-new font-size-26' wx:for="{{item.cartShopGroupList.list}}" wx:for-item="i" wx:for-index="index" wx:key="groupId">
                  <view style='width:90%'>
                    <view class="ellipsis">[商品{{index+1}}]{{i.skuName}}</view>
                  </view>
                  <view>
                    <text>x</text>{{i.skuNumber}}
                  </view>
                </view>
              </view>
            </view>
            <!-- 是否需要押桶 -->
            <view wx:if="{{item.isBarreledWater&&canBucket&&store&&store.yatong}}" class="padding-lr-30 font-size-28 flex justify-content-between align-items-center margin-bottom-10">
              <text class="color-red">是否需要押桶</text>
              <switch class="wx-switch-input" bindchange="changeBucketStatus" data-index="{{index}}" data-productid="{{item.cartShop?item.cartShop.skuId:item.cartShopGroupList.groupId}}" data-source="{{item.source}}" checked="{{item.isBuckets}}" color="#3392FE"></switch>
            </view>
            <view wx:if="{{item.isBuckets}}" wx:for="{{item.bucketsList}}" wx:for-item="bucket" wx:for-index="bucketIndex" wx:key="brandId">
              <view class="padding-lr-30 font-size-28 flex justify-content-between align-items-center margin-bottom-10">
                <text>{{bucket.name}}</text>
                <text>¥{{bucket.price}}/个</text>
              </view>
              <view class="padding-lr-30 font-size-28 flex justify-content-between align-items-center margin-bottom-10">
                <text>押桶数量：</text>
                <view class="flex align-items-center">
                  <view catchtap="reduceBucketNum" data-index="{{index}}" data-bucketindex="{{bucketIndex}}" class="font-size-40 bold reduce-add-button" style="line-height:20rpx;">－</view>
                  <input type="number" bindinput="changeBucketNum" value="{{bucket.num}}" data-index="{{index}}" data-bucketindex="{{bucketIndex}}" class="ya-tong-num"></input>
                  <view catchtap="addBucketNum" data-index="{{index}}" data-bucketindex="{{bucketIndex}}" class="font-size-40 bold reduce-add-button text-align-right" style="line-height:34rpx;">＋</view>
                </view>
              </view>
            </view>
          </view>
        </view>

      </view>

      <view wx:if="{{upPrice > 0}}" class='words margin-top-20'>

        <!-- 是否需要押桶 -->
        <view class=" font-size-28 flex justify-content-between align-items-center margin-bottom-10">
          <text class="color-red">师傅很辛苦，是否打赏爬楼费</text>
          <view style="display: flex;align-items: center;">
            <view class="color-red" style="margin-right: 20rpx;">￥{{upPrice}}元</view>
            <switch class="wx-switch-input" bindchange="changeIsUpPrice" checked="{{isUpPrice}}" color="#3392FE"></switch>
          </view>
        </view>
      </view>
      <!-- 是否自提商品 -->
      <view class='words margin-top-20' wx:if="{{drainageuserid}}">
        <view style="display: flex;justify-content: space-between;align-items: center;height: 60rpx;">
          <view style="font-size: 28rpx;font-family: PingFang SC;font-weight: 600;color: #FF2F2F;">本商品需自提</view>
          <view style="font-size: 24rpx;font-family: PingFang SC;font-weight: 400;color: #3392FE;">截止时间：{{drainage.endTimeStr}}</view>
        </view>
        <view class=" weui-cells_after-title wx-words margin-top-10">
          <view>团长：{{drainageUser.username || '-'}}</view>
          <view>联系方式：{{drainageUser.mobile || '-'}}</view>
          <view>自提地址：{{drainageUser.address || '-'}}</view>
        </view>
      </view>
      <!-- 留言 -->
      <view class='words margin-top-20'>
        <view style="display: flex;justify-content: space-between;align-items: center;height: 60rpx;">
          <view style="font-size: 28rpx;font-family: PingFang SC;font-weight: 600;color: #FF2F2F;">备注留言</view>
          <view style="font-size: 24rpx;font-family: PingFang SC;font-weight: 400;color: #3392FE;">给水站、送水员留言</view>
        </view>
        <view class=" weui-cells_after-title wx-words margin-top-10">
          <view>
            <textarea wx:if="{{!(showModalStatus||showModalBillStatus)}}" class="weui-textarea" placeholder='填写留言' style="height: 80rpx" focus='{{onFocus}}' bindblur="onShowText" data-type="remark" bindinput='getRemarks' value='{{remarks}}'></textarea>
            <view class='text' style="height: 80rpx" bindtap='onShowTextarea' wx:else>{{remarks?remarks:'填写留言'}}</view>
          </view>
        </view>
      </view>
      <!-- 是否需要开票 -->
      <!-- <view class="receipt bg-white ">
        <view   class="font-size-26 flex justify-content-between align-items-center margin-bottom-20">
          <text class="color-red">是否需要开票</text>
          <switch class="wx-switch-input" bindchange="changeDrawBill" checked="{{chooseDrawBill}}" color="#3392FE"></switch>
        </view>
        <view wx:if="{{chooseDrawBill}}" class='item2 margin-bottom-20'>
          <view class="flex justify-content-between align-items-center font-size-26" catchtap="goOpenBill" hover-class="weui-cell_active">
            <view class="weui-cell__bd">开票明细</view>
            <view class="weui-cell__ft_in-access">{{invoiceOrder.invoiceType==1?'增值税专用发票':'增值税普通发票'}} ¥{{invoiceMoney}}</view>
          </view>
        </view>
      </view> -->
      <payTypeSelect useTicketNum="{{useTicketNum}}" checkboxselect="{{checkboxselect}}" payType="{{payType}}" padding="big" sexBoxType="1" waterTicket="{{isCanTarget}}" showBank="{{false}}" bindselect="payTypeSelectFun" bindshuipaio="bindchangeticket"></payTypeSelect>

      <view wx:if="{{!useTicketNum || useTicketNum == 0}}" class="coupon-box">
        <view class="coupon-item" bindtap="goSelectTicket">
          <view class="l" wx:if="{{!canuserticket || canuserticket.length == 0}}">
            <text class="name">没有可用的优惠券</text>
            <text class="txt">0张</text>
          </view>
          <view class="l" wx:elif="{{!ticketPrice}}">
            <text class="name">优惠券</text>
            <text class="txt">{{canuserticket.length}}张</text>
          </view>
          <view class="l" wx:else>
            <text class="name">优惠券</text>
            <text class="txt">-￥{{ticketPrice}}元</text>
          </view>
          <view class="r">
            <!-- <van-icon name="arrow" />       -->
          </view>
        </view>
      </view>
      <view wx:if="{{payType=='水票支付'}}" class="padding-lr-30 color-red font-size-24">你已选用水票，订单实付金额为0元，系统智能识别为水票支付。 您可直接点击”去支付“完成下单
      </view>
      <view class='bg-white b-t-10 margin-top-20 padding-tb-20' wx:if="{{showCloseTip}}">
        <view class='font-size-28 color-red padding-lr-30'>
          <view>本店24小时可下单。现在店铺休息中。</view>
          <view>营业时间：{{store.storeServicetime}}，营业后将优先为您配送!</view>
        </view>
      </view>


      <view class='item2 b-t-10 margin-top-20 padding-tb-20'>
        <!-- 优惠商品 -->
        <view class='tt df' wx:if="{{totalOriginal != '0.00'}}">
          <text>商品合计</text>
          <view>
            <text>+</text>
            <text class='money'>￥</text>
            <text class='price'>{{totalOriginal}}</text>
          </view>
        </view>
        <!-- <view class='tt df' wx:if="{{groomsTotalOriginal != '0.00'}}">
        <text>套餐原价</text>
        <view>
          <text>+</text>
          <text class='money'>￥</text>
          <text class='price'>{{groomsTotalOriginal}}</text>
        </view>
      </view> -->
        <view class='tt df' wx:if="{{groomsTotalDiscounts != '0.00'}}">
          <text>套餐优惠</text>
          <view class="color-red">
            <text>-</text>
            <text class='money'>￥</text>
            <text class='price'>{{groomsTotalDiscounts}}</text>
          </view>
        </view>
        <!-- <view class='tt df' wx:if="{{goodsTotalOriginal != '0.00'}}">
        <text>商品原价</text>
        <view class="color-red">
          <text>-</text>
          <text class='money'>￥</text>
          <text class='price'>{{goodsTotalOriginal}}</text>
        </view>
      </view> -->
        <view class='tt df' wx:if="{{goodsTotalDiscounts != '0.00'}}">
          <text>商品优惠</text>
          <view class="color-red">
            <text>-</text>
            <text class='money'>￥</text>
            <text class='price'>{{goodsTotalDiscounts}}</text>
          </view>
        </view>
        <!-- <view class='tt df' wx:if="{{goodsTotal != '0.00' && goodsTotal != goodsTotalOriginal}}">
        <text>商品优惠价</text>
        <view>
          <text>+</text>
          <text class='money'>￥</text>
          <text class='price'>{{goodsTotal}}</text>
        </view>
      </view> -->

        <!-- 押桶金合计 -->
        <view class='tt df' wx:if="{{bucketMoneyTotal != '0.00'}}">
          <view>
            <text>押桶金合计</text>
            <text class="color-grey-new margin-left-20">押桶金不可申请开发票</text>
          </view>
          <view>
            <text>+</text>
            <text class='money'>￥</text>
            <text class='price'>{{bucketMoneyTotal}}</text>
          </view>
        </view>
        <!-- 上楼费 -->
        <view class='tt df' wx:if="{{freightAndUpfloor != '0.00'}}">
          <text>客户付上楼费总额</text>
          <view class='df'>
            <text class='money'>+</text>
            <text class='money'>￥</text>
            <text class='price'>{{freightAndUpfloor? freightAndUpfloor : "0.00"}}</text>
          </view>
        </view>


        <view class='tt df' wx:if="{{activityGoodsPostage > 0 }}">
          <text>快递费用</text>
          <view class='df'>
            <text class='money'>+</text>
            <text class='money'>￥</text>
            <text class='price'>{{activityGoodsPostage}}</text>
          </view>
        </view>
        <view class='tt df' wx:if="{{upPrice > 0  && isUpPrice}}">
          <text>爬楼费(点击打赏爬楼费有此项)</text>
          <view class="color-red">
            <text class='money'>+</text>
            <text class='money'>￥</text>
            <text class='price'>{{upPrice}}</text>
          </view>
        </view>
        <view class='tt df' wx:if="{{store.minnumber > totalNum && store.minprice > 0}}">
          <view style="display: flex;align-items: center;">
            <text>配送费</text>
            <view style="margin-left: 20rpx;padding: 0 20rpx;height: 33rpx;font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 24rpx;color: #EE0A24;line-height: 33rpx;border-radius: 6rpx;border: 1rpx solid #EE0A24;">满{{store.minnumber}}件就可以免配送费哦~</view>
          </view>
          <view class="color-red">
            <text class='money'>+</text>
            <text class='money'>￥</text>
            <text class='price'>{{yunfei}}</text>
          </view>
        </view>

        <view class='tt df' wx:if="{{orderTotalPrice != '0.00'}}">
          <text>订单总金额</text>
          <view class="color-red">
            <text>+</text>
            <text class='money'>￥</text>
            <text class='price'>{{orderTotalPrice}}</text>
          </view>
        </view>
        <view class='tt df' wx:if="{{useTicketNum != 0 }}">
          <text>其中 水票抵扣</text>
          <view>
            <text>-</text>
            <text class='money'>￥</text>
            <text class='price'>{{discount}}</text>
          </view>
        </view>
        <view class='tt df' wx:if="{{ticketPrice > 0 }}">
          <text>优惠券抵扣</text>
          <view>
            <text>-</text>
            <text class='money'>￥</text>
            <text class='price'>{{ticketPrice}}</text>
          </view>
        </view>
        <!-- <view class='item2 margin-bottom-10'>
          <view class="flex justify-content-between align-items-center font-size-26" bindtap='goExplainInfo' hover-class="weui-cell_active">
            <view class="weui-cell__bd">配送说明</view>
            <view class="weui-cell__ft_in-access">配送说明</view>
          </view>
        </view> -->
        <view class='tt df' bindtap='goExplainInfo'>
          <text>配送说明</text>
          <view>
            配送说明 >
          </view>
        </view>
      </view>
    </view>
    <view class="padding-big bg-white margin-top-20 b-t-10 text-align-right font-size-26">
      <view>
        <text class="margin-right-10">已优惠</text>
        <text class="color-red margin-right-10">－¥{{showDiscountTotal}}</text>
        <text class="margin-right-10">小计</text>
        <text class="font-size-46"><text class="font-size-26">￥</text>{{total}}</text>
      </view>
      <view wx:if="{{bucketMoneyTotal != '0.00'}}" class="color-red font-size-26">您选择了线上押桶方式，需先支付押桶金.</view>
    </view>
    <view style="height:100rpx"></view>
  </scroll-view>
  <view bindtap="goAddGood" wx:if="{{store.minnumber > totalNum && store.minprice > 0}}" class="cart-bottom1 df">
    <view style="padding: 0 50rpx;display: flex;align-items: center;width: 100%;height: 100%;justify-content: space-between;">
      <view style="padding: 0 20rpx;height: 33rpx;font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 24rpx;color: #EE0A24;line-height: 33rpx;border-radius: 6rpx;border: 1rpx solid #EE0A24;">满{{store.minnumber}}件就可以免配送费哦~</view>
      <view style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 24rpx;background-color: #1971F2;padding: 0 20rpx;height: 50rpx;line-height: 50rpx;color: white;border-radius: 16rpx;">去凑单</view>
    </view>
  </view>
  <view class='footer'>
    <view class='cont df'>
      <view class="ellipsis">
        <text class='money'>￥</text>
        <text class='price'>{{total}}</text>
        <text wx:if="{{bucketMoneyTotal != '0.00'}}" class="font-size-24 margin-left-30">其中桶押金合计：¥{{bucketMoneyTotal}}</text>
      </view>
      <view class='button' bindtap='goSettlement'>去支付</view>
    </view>
  </view>
  <!-- 押金支付 view -->
  <view class='deposit_pay' wx:if="{{is_depositPay}}" style='height:{{windowHeight}}rpx'>
    <view class='deposit_pay_box' style=' text-align:center;'>
      <view class='title'>
        <view class='close' bindtap='closeDepositPay'>
          <image src='/images/pop-close-icon.png'></image>
        </view>
        <view class='text'>余额支付</view>
      </view>
      <view class="jine">金额</view>
      <view class='jine_num'>
        <text class='icon'>￥</text>
        <text class='num'>{{total}}</text>
      </view>
      <paySix bindvalueSix="valueSix" input_value="{{inputData.input_value}}" value_length="{{inputData.value_length}}" isNext="{{inputData.isNext}}" get_focus="{{true}}" focus_class="{{inputData.focus_class}}" value_num="{{inputData.value_num}}" height="{{inputData.height}}" width="{{inputData.width}}" see="{{inputData.see}}" interval="{{inputData.interval}}">
      </paySix>
      <view class='forget_pwd' bindtap='forgetPwd'>
        <text>忘记密码？</text>
      </view>
    </view>
  </view>
</view>
<payConfirmSelect showModalStatus="{{showModalStatus}}" animationData="{{animationData}}" payTypeList="{{bucketMoneyPayTypeList}}" first="{{paymentDescriptionFirst}}" second="{{paymentDescriptionSecond}}" third="{{paymentDescriptionThird}}" total="{{(payType=='月结付款'&&bucketMoneyTotal>0)?bucketMoneyTotal:total}}" bindclose="hideModal" bindchange="payChange" bindpay="payMoney"></payConfirmSelect>
<!-- showModalStatus -->
<view class="commodity_screen" bindtap="hideModal" catchtouchmove wx:if="{{showModalBillStatus}}"></view>
<view class="invoice_information_box" wx:if="{{showModalBillStatus}}">
  <invoiceInformation height="740rpx" isModal="{{true}}" invoice="{{invoiceOrder}}" bindconfirm="invoiceInfoDataFun"></invoiceInformation>
</view>

<view class='mask-guaid-image' wx:if="{{showMaskGuide&&isCanTarget}}">
  <view class="left-top" wx:if="{{showGuideImage!=1}}" catchtap="showNextImageLast" style="position:absolute">跳过</view>
  <image wx:if="{{showGuideImage==1}}" catchtap="showNextImageLast" src="{{imgUri}}/images/maskGuide/keyongshuipiao-one.png?v={{random}}"></image>
</view>
<!-- 选择地址弹窗 -->
<view wx:if="{{showModalAddress}}" class="shadowbg">
  <view class="noticebg11">
    <view style="background-color: white;width: 640rpx;border-radius: 16rpx;">
      <view style="padding: 20rpx;text-align: center;">
        <view style="font-family: PingFangSC, PingFang SC;font-weight: 600;font-size: 40rpx;color: #323233;padding-bottom: 20rpx;">温馨提示</view>
        <view style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 32rpx;color: #323233;padding: 10rpx 0;">你似乎不在默认地址附近，请确认地址</view>
        <view style="display: flex;justify-content: space-between;padding: 10rpx;background: rgba(25,113,242,0.1);border-radius: 4rpx;border: 1rpx solid #1971F2;align-items: center;">
          <view style="font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 30rpx;color: #0D0318;text-align: left;">
            <view style="display: flex;align-items: center;">
              <view style="width: 64rpx;height: 40rpx;line-height: 40rpx;background: #1971F2;border-radius: 8rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 20rpx;color: #FFFFFF;text-align: center;margin-right: 10rpx;">默认</view>{{addressInfo.province + addressInfo.area + addressInfo.city}}
            </view>
            <view>{{addressInfo.street}}</view>
          </view>
          <view bindtap="addressConfirm">
            <view style="width: 140rpx;height: 80rpx;line-height: 80rpx;background: #1971F2;border-radius: 40rpx;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 28rpx;color: #FFFFFF;text-align: center;">确认</view>
          </view>
        </view>
        <view bindtap="chooseInfo" style="margin-top: 20rpx;display: flex;justify-content: space-between;padding: 10rpx;border-radius: 4rpx;border: 1rpx solid #19C66C;align-items: center;">
          <view style="font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 30rpx;color: #19C66C;text-align: left;margin-left: 20rpx;">
            <view>重新选择</view>
          </view>
          <view>
            <view style="width: 140rpx;height: 80rpx;line-height: 80rpx;text-align: center;display: flex;align-items: center;justify-content: center;">
              <image style="width: 32rpx;height: 32rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240416/a99a077d553943299aba44b8d8c5d738.png" mode="" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>