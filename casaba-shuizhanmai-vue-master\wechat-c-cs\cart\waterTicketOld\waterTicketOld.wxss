.cartMain {
  /* margin-bottom: 120rpx; *//* margin-top: 40rpx; */
}

.tips {
  font-size: 26rpx;
  color: #888;
  padding: 24rpx;
  box-sizing: border-box;
  height: 90rpx;
}

.cart_main {
  border-radius: 15rpx;
  /* width: 76%; */
  width: 568rpx;
  min-height: 210rpx;
  /* height:250rpx; */
  padding: 0 35rpx;
  background: #fff;
}

.cart_main_new {
  width: 623rpx;
  height: 200rpx;
}

.cart-image {
  width: 212rpx;
  height: 159rpx;
  padding: 15rpx 10rpx;
}

.cart-box {
  width: 100%;
  padding: 12rpx 0;
  /* margin-top: 15rpx; *//* padding-left: 60rpx; */
}

.icon {
  margin: 85rpx 20rpx;
}

.icon image {
  width: 44rpx;
  height: 44rpx;
}

.icon_new image {
  width: 44rpx;
  height: 44rpx;
}

/* 颜色更改 */

.no-use {
  font-size: 24rpx;
  color: #999;
  margin-left: 90rpx;
}

.left .dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background: #000;
  margin: 7rpx auto;
}

.left .i2 {
  margin: 10rpx auto;
}

.left .img {
  width: 32rpx;
  height: 46rpx;
}

.left {
  width: 180rpx;
  font-size: 18rpx;
  text-align: center;
}

.left .dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
}

.left .num {
  margin-left: 5rpx;
}

.right {
  margin-left: 7%;
  font-size: 28rpx;
  color: #000;
  width: 65%;
  line-height: 38rpx;
}

.right .line {
  width: 64rpx;
  height: 2rpx;
  background: #000;
  display: block;
  margin: 0 10rpx;
}

/* 颜色更改结束 */

.rule {
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 260rpx;
}

.botton-box {
  position: fixed;
  bottom: 30rpx;
  width: 90%;
  left: 5%;
  border-radius: 15rpx;
  background: #fff;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.4);
  background: linear-gradient(to right, #1794fd, #68ddff);
  color: #fff;
  font-size: 36rpx;
  height: 88rpx;
  text-align: center;
  line-height: 88rpx;
}

.footer {
  position: fixed;
  bottom: 0;
  height: 170rpx;
  background: #fff;
  padding: 0 18rpx 0 18rpx;
  width: 100%;
  /* border-radius: 16rpx 16rpx 0 0; */
  box-sizing: border-box;
  z-index: 1000;
}

.botton-box-new {
  width: 700rpx;
  text-align: center;
  font-size: 30rpx;
  background: #3392fe;
  padding: 10rpx 0;
  border-radius: 36rpx;
  color: #fff;
}

.color-blue-new {
  color: #3392fe;
}
