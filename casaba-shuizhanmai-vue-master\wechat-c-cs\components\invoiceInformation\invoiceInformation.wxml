<view class="invoice-information-index" catchtouchmove="preventD">
  <view style="background: #FFFFFF;border-radius: 16rpx;">
    <view style="display: flex;align-items: center;padding: 20rpx;justify-content: space-between;align-items: center;">
      <view style="font-family: PingFangSC, PingFang SC;font-weight: 600;font-size: 32rpx;color: #323233;">抬头类型</view>
      <view style="display: flex;font-family: PingFangSC, PingFang SC;font-weight: 600;font-size: 28rpx;color: #323233;">
        <view style="display: flex;align-items: center;" bindtap="changetttt" data-result="0">
          <image wx:if="{{invoiceInfo.riseType == 0}}" src='{{imgUri}}/images/choose.png' mode="widthFix" style="width:38rpx"></image>
          <image wx:else src='{{imgUri}}/images/unchoose.png' mode="widthFix" style="width:38rpx"></image>
          <view style="margin-left: 10rpx;">个人</view>
        </view>
        <view style="display: flex;align-items: center;margin-left: 60rpx;" bindtap="changetttt" data-result="1">
          <image wx:if="{{invoiceInfo.riseType == 1}}" src='{{imgUri}}/images/choose.png' mode="widthFix" style="width:38rpx"></image>
          <image wx:else src='{{imgUri}}/images/unchoose.png' mode="widthFix" style="width:38rpx"></image>
          <view style="margin-left: 10rpx;">公司</view>
        </view>
      </view>
    </view>
    <view style="padding: 20rpx;font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 28rpx;color: #0D0318;" wx:if="{{invoiceInfo.riseType == 0}}">个人开具发票最低金额<text style="color: #B11111;">{{store.invoice ? store.invoice : 0}}元以上</text>，金额不够可以累计，时间1年内有效</view>
  </view>
  <view class="font-size-28 bold" style="margin: 20rpx 0;">
    发票类型
  </view>
  <view style="background: #FFFFFF;border-radius: 16rpx;" wx:if="{{invoiceInfo.riseType == 1}}">
    <view style="display: flex;align-items: center;padding: 20rpx;justify-content: space-around;align-items: center;">
      <view style="display: flex;font-family: PingFangSC, PingFang SC;font-weight: 600;font-size: 28rpx;color: #323233;justify-content: space-around;">
        <view style="display: flex;align-items: center;" bindtap="changeInvoiceType" data-type="0">
          <image wx:if="{{invoiceInfo.invoiceType==0}}" src='{{imgUri}}/images/choose.png' mode="widthFix" style="width:38rpx"></image>
          <image wx:else src='{{imgUri}}/images/unchoose.png' mode="widthFix" style="width:38rpx"></image>
          <view style="margin-left: 10rpx;">普通发票-电子</view>
        </view>
        <view style="display: flex;align-items: center;margin-left: 60rpx;" bindtap="changeInvoiceType" data-type="1">
          <image wx:if="{{invoiceInfo.invoiceType==1}}" src='{{imgUri}}/images/choose.png' mode="widthFix" style="width:38rpx"></image>
          <image wx:else src='{{imgUri}}/images/unchoose.png' mode="widthFix" style="width:38rpx"></image>
          <view style="margin-left: 10rpx;">增值税专用发票</view>
        </view>
      </view>
    </view>
    <view style="padding: 20rpx;font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 28rpx;color: #0D0318;" wx:if="{{invoiceInfo.invoiceType == 0}}">开具发票最低金额<text style="color: #B11111;">{{store.putonginvoice ? store.putonginvoice : 0}}元以上</text>，金额不够可以累计，时间1年内有效</view>
    <view style="padding: 20rpx;font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 28rpx;color: #0D0318;" wx:if="{{invoiceInfo.invoiceType == 1}}">开具发票最低金额<text style="color: #B11111;">{{store.zhuaninvoice ? store.zhuaninvoice : 0}}元以上</text>，金额不够可以累计，时间1年内有效</view>
  </view>
  <view style="background: white;padding: 20rpx;margin-top: 30rpx;border-radius: 16rpx;">
    <view wx:if="{{invoiceInfo.riseType==1}}" class="flex  font-size-30 align-items-center width-full">
      <text class="invoice-title">*单位名称</text>
      <input value="{{invoiceInfo.unitName}}" cursor-spacing="100" bindinput="inputEdit" bindfocus="unitNameFocus" bindblur="unitNameBlur" data-name="invoiceInfo.unitName" class="width-full" placeholder="请填写单位名称"></input>
    </view>
    <view wx:if="{{showInvoiceHistory}}" class="show-title-tip">
      <view wx:for="{{invoiceHistoryList}}" wx:if="{{index<3}}" wx:key="name" catchtap="chooseInvoiceHistory" data-index="{{index}}" class="color-blue padding-tb-10 ellipsis">{{item.unitName}}</view>
    </view>
    <view wx:if="{{invoiceInfo.riseType==1}}" class="flex margin-top-30 font-size-30 align-items-center width-full">
      <text class="invoice-title">*税号</text>
      <input value="{{invoiceInfo.taxNum}}" bindinput="inputEdit" data-name="invoiceInfo.taxNum" class="width-full" placeholder="请填写纳税人识别号"></input>
    </view>
    <view wx:if="{{invoiceInfo.invoiceType==1&&invoiceInfo.riseType==1}}" class="flex margin-top-30 font-size-30 align-items-center width-full">
      <text class="invoice-title">*单位地址</text>
      <input value="{{invoiceInfo.unitAddress}}" bindinput="inputEdit" data-name="invoiceInfo.unitAddress" class="width-full" placeholder="请填写单位地址"></input>
    </view>
    <view wx:if="{{invoiceInfo.invoiceType==1&&invoiceInfo.riseType==1}}" class="flex margin-top-30 font-size-30 align-items-center width-full">
      <text class="invoice-title">*单位电话</text>
      <input value="{{invoiceInfo.unitPhone}}" bindinput="inputEdit" data-name="invoiceInfo.unitPhone" class="width-full" placeholder="请填写单位电话"></input>
    </view>
    <view wx:if="{{invoiceInfo.invoiceType==1&&invoiceInfo.riseType==1}}" class="flex margin-top-30 font-size-30 align-items-center width-full">
      <text class="invoice-title">*开户银行</text>
      <input value="{{invoiceInfo.bankName}}" bindinput="inputEdit" data-name="invoiceInfo.bankName" class="width-full" placeholder="请填写开户行"></input>
    </view>
    <view wx:if="{{invoiceInfo.invoiceType==1&&invoiceInfo.riseType==1}}" class="flex margin-top-30 font-size-30 align-items-center width-full">
      <text class="invoice-title">*银行账号</text>
      <input value="{{invoiceInfo.bankNum}}" bindinput="inputEdit" data-name="invoiceInfo.bankNum" class="width-full" placeholder="请填写银行账号"></input>
    </view>
    <view class="flex margin-top-30 font-size-30 align-items-center width-full">
      <text class="invoice-title">更多内容</text>
      <input value="{{invoiceInfo.moreContent}}" bindinput="inputEdit" maxlength="20" data-name="invoiceInfo.moreContent" class="width-full" placeholder="请填写备注、地址等（非必填）"></input>
    </view>
  </view>
  <!-- <view class="font-size-28 bold margin-top-30">
      发票接收方式
    </view>
    <view class="flex margin-top-20 font-size-24 align-items-center width-full">
      <text class="invoice-title">*接收邮箱</text>
      <input value="{{invoiceInfo.email}}" bindinput="inputEdit" data-name="invoiceInfo.email" class="width-full" placeholder="请填写接收发票邮箱"></input>
    </view>
    <view class="flex margin-top-20 font-size-24 align-items-center width-full">
      <text class="invoice-title">*接收联系方式</text>
      <input value="{{invoiceInfo.mobile}}" bindinput="inputEdit" data-name="invoiceInfo.mobile" class="width-full" placeholder="请填写接收发票联系方式"></input>
    </view> -->
  <!-- <view class="flex font-size-24 margin-top-30">
      <view wx:if="{{!acceptTypeOnly}}" catchtap="changeGetInvoiceType" data-type="0" class="margin-right-button invoice-information-type {{invoiceInfo.acceptType==0?'choose':''}}">随送货接收</view>
      <view catchtap="changeGetInvoiceType" data-type="1" class="invoice-information-type {{invoiceInfo.acceptType==1?'choose':''}}">快递接收</view>
    </view>
    <view wx:if="{{invoiceInfo.acceptType==1}}" class="flex margin-top-30 font-size-30 align-items-center width-full">
      <text class="invoice-title">*邮寄地址</text>
      <picker mode="multiSelector" bindchange="bindRegionChange" bindcolumnchange="columnChange" range="{{areaRange}}" range-key="name" class="width-full">
        <view class="flex align-items-center justify-content-end width-full">
          <text>{{invoiceInfo.acceptAddress?invoiceInfo.acceptAddress:'请选择'}}</text>
          <image class="margin-left-30" src='{{imgUri}}/images/shop/r-black-icon.png' mode="widthFix" style="width:9rpx"></image>
        </view>
      </picker>
    </view>
    <view wx:if="{{invoiceInfo.acceptType==1}}" class="flex margin-top-20 font-size-24 align-items-center width-full">
      <text class="invoice-title"></text>
      <input value="{{invoiceInfo.acceptAddressMore}}" bindinput="inputEdit" data-name="invoiceInfo.acceptAddressMore" class="width-full" placeholder="请填写详细地址"></input>
    </view>
    <view wx:if="{{invoiceInfo.acceptType==1}}" class="flex margin-top-20 font-size-24 align-items-center width-full">
      <text class="invoice-title">*收件人姓名</text>
      <input value="{{invoiceInfo.acceptName}}" bindinput="inputEdit" data-name="invoiceInfo.acceptName" class="width-full" placeholder="请填写收件人姓名"></input>
    </view>
    <view wx:if="{{invoiceInfo.acceptType==1}}" class="flex margin-top-20 font-size-24 align-items-center width-full">
      <text class="invoice-title">*收件人手机号</text>
      <input value="{{invoiceInfo.acceptPhone}}" type='number' maxlength="11" bindinput="inputEdit" data-name="invoiceInfo.acceptPhone" class="width-full" placeholder="请填写收件人手机号"></input>
    </view> -->
  <view style="margin-top: 30rpx;">
    <view style="display: flex;justify-content: space-between;padding: 20rpx;">
      <view>请添加微信</view>
      <view bind:tap="copyText" style="font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 34rpx;color: #1971F2;text-align: left;font-style: normal;text-decoration-line: underline;">复制微信号</view>
    </view>
    <view style="background-color: white;border-radius: 16rpx;padding: 20rpx;font-family: PingFangSC, PingFang SC;font-weight: 400;font-size: 34rpx;color: #B11111;line-height: 48rpx;text-align: left;font-style: normal;">
      <view>请添加微信号：{{store.wechat || store.storePhone}}</view>
      <view>我们会尽快开好电子发票，通过微信发给您！</view>
    </view>
  </view>
  <view catchtap="confirmInvoiceInfo" data-type="confirm" class="font-size-30 invoice-info-button">{{isModal?'确定':'提交'}}</view>
</view>