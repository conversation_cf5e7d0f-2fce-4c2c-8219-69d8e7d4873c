@import "../../app.wxss";

/* 支付弹窗start */

/* 底部弹出层 */

.commodity_screen {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
  overflow: hidden;
  z-index: 98;
  color: #fff;
}

.commodity_attr_box {
  width: 100%;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background: #fff;
  height: 800rpx;
  padding: 30rpx 0;
}

.confirm-select .close {
  width: 34rpx;
  height: 34rpx;
  background: #f5f5f5;
  border-radius: 50%;
}

.pay-title {
  color: #000;
  font-size: 30rpx;
  text-align: center;
}

.pay-title .bigMoney {
  font-size: 72rpx;
  color: #333;
  font-weight: bold;
}

.pay-type {
  min-height: 50rpx;
  font-size: 30rpx;
  padding: 30rpx 10rpx;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pay-type-label {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.pay_card_img {
  width: 36rpx;
  height: 36rpx;
}

.confirm-modal {
  width: 100%;
  padding: 88rpx 0 50rpx 0;
  text-align: center;
}

.pay-confirm-btn {
  margin: 0 auto;
  width: 680rpx;
  height: 70rpx;
  line-height: 70rpx;
}

.confirm-btn {
  width: 90%;
  line-height: 70rpx;
  height: 70rpx;
  border-radius: 36rpx;
  color: #fff;
  font-size: 30rpx;
  background: #3392fe;
}

/* 支付弹窗end */
