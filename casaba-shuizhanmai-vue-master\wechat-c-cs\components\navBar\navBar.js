var sliderWidth = 96; // 需要设置slider的宽度，用于计算中间位置
Component({
  properties: {
    c_tabs:{
      type:Array,
      value: ["桶管理", "款管理"],
    },
    c_numArr:{
      type:Array,
      value:[],
    },
    c_index:{
      type:Number,
      value:0
    },
    c_slider:{
      type:Number,
      value:0,
    },
    c_lineTop:{
      type:Number,
      value:35,
    },
    c_lineShow:{
      type:Number,
      value:0,
    }
  },
  data: {
    activeIndex:0,
    sliderOffset: 0,
    sliderLeft: 0,
  },

  ready: function () {
    let that = this;
    that.setData({
      activeIndex:that.data.c_index
    })
    wx.getSystemInfo({
      success: function (res) {
        that.setData({
          sliderLeft: (res.windowWidth / that.data.c_tabs.length - sliderWidth) / 2 - that.data.c_slider,
          sliderOffset: res.windowWidth / that.data.c_tabs.length * that.data.c_index
        });
      }
    });
  },
  methods: {
    _tabClick: function (e) {
      this.setData({
        sliderOffset: e.currentTarget.offsetLeft,
        activeIndex: e.currentTarget.id
      });
      this.triggerEvent("myEvent", { index: e.currentTarget.id});
    },
  }

})