<!--components/payTypeSelect/payTypeSelect.wxml-->
<view class="bg-white margin-top-20 margin-bottom-20 radius-10 {{padding=='big'?'padding-big font-size-28':'padding font-size-24'}}">


  <view class='df' catchtap='selectSex'>
    <view class="weui-cell__bd">{{padding=='big'?"订单支付方式":"支付方式"}}</view>


    <view wx:if="{{sexBoxType != 1 && sexBoxType != 2 && sexBoxType != 5 }}">
      <view wx:if="{{padding!='big'}}" class="weui-cell__ft_in-access">{{payType}}</view>
      <view wx:else>
        <view class="flex align-items-center">
          <text class="margin-right-20">{{payType}}</text>
          <image src="https://waterstation.com.cn/szm/images/red-go-next.png" style="width:26rpx;height:22rpx;"></image>
        </view>
      </view>
    </view>
  </view>

  <radio-group bindchange="selectPayType" wx:if="{{sexBoxType == 1 || sexBoxType == 2 || sexBoxType == 5}}">
    <view class="pay-item" wx:for="{{payTypeList}}" wx:for-item="item">
      <view class="pay-item-left">
        <image src="{{item.img}}" style="width: 48rpx;height: 48rpx;"></image>
        <text class="paly-item-text">{{item.text}}</text>
      </view>
      <view class="pay-item-right">
        <radio value="{{item.text}}" checked="{{item.text == payType}}" disabled="{{item.disabled}}" />
      </view>
    </view>
  </radio-group>

  <view wx:if="{{showBank&&payType=='银行转账'&&companyTitle}}" class="margin-top-10">水站的公司抬头：{{companyTitle}}</view>
  <view wx:if="{{showBank&&payType=='银行转账'&&bankCard}}" class="margin-top-10">水站的银行账号：{{bankCard}}</view>
  <view wx:if="{{showBank&&payType=='银行转账'&&openingBank}}">水站的开户行：{{openingBank}}</view>
</view>