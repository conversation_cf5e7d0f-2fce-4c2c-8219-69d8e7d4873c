// components/auth-popup/auth-popup.js
Component({
  properties: {
    visible:{
      type: Boolean,
      value: false,
      observer: "_visibleChange"
    }
  },
  data: {
    show:false
  },
  pageLifetimes:{
    show: function(){
		//接受参数
        var visible = this.data.visible;
		this._visibleChange(visible);
    }
  },
  methods: {
    _visibleChange:function(){	
      var visible = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
	  this.setData({
		  show: visible
	  });
    },
	onAfterHidden:function(that){
		that.setData({
			show:false,
		});
			  
		that.triggerEvent("hidden", {
		  visible: false
		});
	},
    onHidden:function(){
		wx.clearStorageSync();
		this.onAfterHidden(this);
    },
	agreePolicy:function(target){
		var that = this ;
		var operator = target.currentTarget.dataset.att || o.att || "disagree" ;
		if(operator == "disagree"){ //不同意
			wx.clearStorageSync() ;
			that.onAfterHidden(that);
		}else{
			//判断用户是否
			wx.setStorageSync("newUserAgreePolicy", true) ; //新用户
			that.onAfterHidden(that);
		}
	},getPhoneNumber:function(res){
		var that = this ;
		//判断用户是否
		wx.setStorageSync("newUserAgreePolicy", true) ; //新用户
		that.onAfterHidden(that);
		//触发
		that.triggerEvent("phonechange",{data:res});
		
	}
  }
})
