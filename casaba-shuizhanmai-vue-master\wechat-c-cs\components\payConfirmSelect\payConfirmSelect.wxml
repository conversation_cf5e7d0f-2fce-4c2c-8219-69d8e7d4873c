<!-- showModalStatus -->
<view class="commodity_screen" bindtap="hideModal" catchtouchmove wx:if="{{showModalStatus}}"></view>
<view animation="{{animationData}}" catchtouchmove class="commodity_attr_box" wx:if="{{showModalStatus}}">
  <!-- 支付价格 -->
  <view class="flex justify-content-between align-items-start box-sizing padding-lr-20 confirm-select">
    <view class="font-size-24 color-grey-new width-full text-align-center">
      <view style="height:40rpx">{{first}}</view>
      <view style="height:40rpx">{{second}}</view>
    </view>
    <view catchtap="hideModal" class="flex justify-content-center align-items-center close">
      <image src="{{imgUri}}/images/tipCloseBlack.png" style="width:14rpx" mode="widthFix"></image>
    </view>
  </view>
  <view class='pay-title'>
    <view class='bigMoney'>
      <text style='font-size:36rpx;'>￥</text>{{total}}</view>
  </view>
  <view class="font-size-24 text-align-center margin-bottom-20">{{third}}</view>
  <!-- 支付方式 -->
  <!-- 押桶金支付 -->
  <radio-group class="radio-group padding-lr-20">
    <view wx:for="{{payTypeList}}" wx:key="name" class="pay-type" catchtap="payChange" data-index="{{index}}">
      <label class="pay-type-label align-items-center">
        <view class="flex align-items-center">
          <image class="pay_card_img " src="{{item.imgUri}}"></image>
          <view class="fl" style="margin-left:42rpx;font-size:36rpx;">
            <view class="bold">{{item.name}}</view>
			<view wx:if="{{item.name=='银行转账' && companyTitle}}" class="font-size-30">{{companyTitle}}</view>
            <view wx:if="{{item.name=='银行转账' && openingBank}}" class="font-size-30">{{openingBank}}</view>
            <view wx:if="{{item.name=='银行转账' && bankCard}}" class="font-size-30">{{bankCard}}</view>
          </view>
        </view>
        <radio checked="{{index==0}}" class="fr" color="#3392FE" value="{{item.name}}" />
      </label>
    </view>
  </radio-group>

  <!-- 确认支付按钮 -->
  <view class='confirm-modal'>
    <view bindtap='payMoney' class='confirm-btn pay-confirm-btn'>
      确认支付
    </view>
  </view>
</view>