<!-- 水票 -->
<view class='page'>
  <view class='cartMain'>
    <view class='tips'>Tips: 请勾选您想要使用的水票。</view>
    <scroll-view scroll-y style='height:calc(100vh - 260rpx);' class="bg-white">
      <view wx:for="{{cartItems}}" wx:key="relevancesId">
        <view data-id="{{item.id}}" class='cart-box df bg-white flex-direction-column' style='transition: all 0.5s; {{list[index].Style}}' data-index="{{index}}">
          <view class="flex font-size-24 width-full padding-lr-20 box-sizing justify-content-between margin-bottom-10">
            <text class="bold" style="margin-left: 90rpx;color:#4ece1c;">本次交易该水票可直接兑换商品</text>
            <text class="color-grey">剩余可用{{item.count}}张</text>
          </view>
          <view class="flex align-items-center justify-content-between width-full padding-lr-20 box-sizing">
            <view class="icon_new">
              <view wx:if="{{is_select[index].selected}}">
                <image data-index="{{index}}" bindtap="selectedCart" src='/images/check-yes-blue.png'></image>
              </view>
              <view wx:else>
                <image bindtap="selectedCart" data-index="{{index}}" src='/images/check-no.png'></image>
              </view>
            </view>
            <view class="bg-white">
              <view class='cart_main_new df' style='background:url("https://waterstation.com.cn/szm/images/water-ticket-blue.png");background-size:100% 100%;'>
                <view class='flex align-items-center'>
                  <image src="{{item.waterImg}}" style="width:150rpx;height:150rpx;flex-shrink:0;padding:0 10rpx;"></image>
                  <view class='font-size-30' style="width:260rpx;">{{item.specification}}</view>
                </view>
                <!-- <view class='left margin-left-20 color-blue-new' wx:if="{{item.disprice}}">
                  <view class='font-size-24'>
                    <text>￥</text>
                    <text class='font-size-48 bold'>{{item.disprice}}</text>
                    <view>元/张</view>
                  </view>
                  <view class='font-size-20' style="text-decoration: line-through">
                    <text>￥</text>
                    <text>{{item.price}}</text> 元/张
                  </view>
                </view> -->
                <view class='left margin-left-10 color-blue-new'>
                  <view class='font-size-24 margin-bottom-10'>
                    <text>￥</text>
                    <text class='font-size-48 bold'>{{item.price}}</text>
                    <view>元/张</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view wx:if="{{cantCartItems.length > 0}}" class="font-size-24 width-full margin-top-20 padding-lr-20 box-sizing justify-content-between margin-bottom-10 no-use">
        不可用原因：产品品牌规格不符。
      </view>
      <view wx:for="{{cantCartItems}}" wx:key="relevancesId">
        <view data-id="{{item.id}}" class='cart-box df bg-white flex-direction-column' style='transition: all 0.5s; {{list[index].Style}}' data-index="{{index}}">
          <view class="flex align-items-center justify-content-between width-full padding-lr-20 box-sizing">
            <view class="icon_new">
              <image data-index="{{index}}" src='/images/disabled.png'></image>
            </view>
            <view class="bg-white">
              <view class='cart_main_new df' style='background:url("https://waterstation.com.cn/szm/images/water-ticket-grey.png");background-size:100% 100%;'>
                <view class='flex align-items-center'>
                  <image src="{{item.waterImg}}" style="width:150rpx;height:150rpx;flex-shrink:0;padding:0 10rpx;"></image>
                  <view class='font-size-30' style="width:260rpx;">{{item.specification}}</view>
                </view>
                <!-- <view class='left margin-left-20 color-grey' wx:if="{{item.disprice}}">
                  <view class='font-size-24'>
                    <text>￥</text>
                    <text class='font-size-48 bold'>{{item.disprice}}</text>
                    <view>元/张</view>
                  </view>
                  <view class='font-size-20' style="text-decoration: line-through">
                    <text>￥</text>
                    <text class=''>{{item.price}}</text> 元/张
                  </view>
                </view> -->
                <view class='left margin-left-10 color-grey'>
                  <view class='font-size-24 margin-bottom-10'>
                    <text>￥</text>
                    <text class='font-size-48 bold'>{{item.price}}</text>
                    <view>元/张</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
<view class="footer flex font-size-24 align-items-center">
  <view class='botton-box-new' bindtap='sureUseTicket'>确定使用</view>
</view>